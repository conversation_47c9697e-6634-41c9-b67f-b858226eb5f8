#!/usr/bin/env node

/**
 * 🤖 ACTIVATEUR AGENT JARVIS DEPUIS MÉMOIRE THERMIQUE
 * Activation de l'agent JARVIS intégré dans la mémoire thermique
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');
const readline = require('readline');

class JarvisThermalAgent {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.agentId = 'jarvis_direct_1750002352443';
        this.thermalMemory = null;
        this.jarvisAgent = null;
        this.active = false;
        
        console.log('🤖 ACTIVATEUR AGENT JARVIS THERMAL');
        console.log(`🆔 Agent ID: ${this.agentId}`);
    }

    // Charger l'agent depuis la mémoire thermique
    loadFromThermalMemory() {
        console.log('🌡️ Chargement depuis mémoire thermique...');
        
        if (!fs.existsSync(this.thermalMemoryPath)) {
            throw new Error('❌ Mémoire thermique non trouvée');
        }
        
        this.thermalMemory = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        if (!this.thermalMemory.jarvis_agents || !this.thermalMemory.jarvis_agents[this.agentId]) {
            throw new Error(`❌ Agent JARVIS ${this.agentId} non trouvé dans mémoire thermique`);
        }
        
        this.jarvisAgent = this.thermalMemory.jarvis_agents[this.agentId];
        
        console.log('✅ Agent JARVIS chargé depuis mémoire thermique');
        console.log(`📁 Source: ${this.jarvisAgent.app_path}`);
        console.log(`🔧 Composants: ${Object.keys(this.jarvisAgent.components).length}`);
        console.log(`🧠 QI boost: +${this.jarvisAgent.thermal_integration.qi_boost}`);
        
        return this.jarvisAgent;
    }

    // Activer l'agent
    activate() {
        console.log('🚀 ACTIVATION AGENT JARVIS...');
        
        if (!this.jarvisAgent) {
            this.loadFromThermalMemory();
        }
        
        this.active = true;
        
        // Afficher les capacités de l'agent
        this.displayCapabilities();
        
        console.log('✅ Agent JARVIS activé et prêt !');
        console.log('💬 Tapez vos messages pour interagir avec JARVIS');
        console.log('💡 Commandes: /status, /components, /thermal, /quit');
        
        return true;
    }

    // Afficher les capacités
    displayCapabilities() {
        console.log('\n🎯 CAPACITÉS AGENT JARVIS:');
        console.log('==========================');
        
        // Analyser les composants
        for (const [name, component] of Object.entries(this.jarvisAgent.components)) {
            console.log(`🔧 ${name}:`);
            if (component.has_express) console.log('  ✅ Serveur Express');
            if (component.has_socket) console.log('  ✅ WebSocket');
            if (component.has_thermal) console.log('  ✅ Mémoire Thermique');
            if (component.has_deepseek) console.log('  ✅ DeepSeek Integration');
            if (component.has_brain) console.log('  ✅ Cerveau Artificiel');
            if (component.ports && component.ports.length > 0) {
                console.log(`  🌐 Ports: ${component.ports.join(', ')}`);
            }
        }
        
        // Statut thermal
        console.log('\n🌡️ INTÉGRATION THERMIQUE:');
        console.log(`  🆔 Agent ID: ${this.jarvisAgent.agent_id}`);
        console.log(`  🧠 QI Boost: +${this.jarvisAgent.thermal_integration.qi_boost}`);
        console.log(`  🧬 Neurones: ${this.jarvisAgent.thermal_integration.neural_allocation.toLocaleString()}`);
        console.log(`  📊 Zones mémoire: ${this.jarvisAgent.thermal_integration.memory_zones.join(', ')}`);
        console.log(`  ✅ Statut: ${this.jarvisAgent.thermal_integration.status}`);
    }

    // Traiter un message
    processMessage(message) {
        if (!this.active) {
            return '❌ Agent JARVIS non activé';
        }
        
        // Commandes spéciales
        if (message.startsWith('/')) {
            return this.handleCommand(message);
        }
        
        // Traitement normal avec contexte thermal
        const response = this.generateResponse(message);
        
        // Mettre à jour l'activité dans la mémoire thermique
        this.updateThermalActivity();
        
        return response;
    }

    // Gérer les commandes
    handleCommand(command) {
        switch (command.toLowerCase()) {
            case '/status':
                return this.getStatus();
            case '/components':
                return this.getComponentsInfo();
            case '/thermal':
                return this.getThermalInfo();
            case '/quit':
                this.active = false;
                return '👋 Agent JARVIS désactivé. Au revoir Jean-Luc !';
            default:
                return `❓ Commande inconnue: ${command}`;
        }
    }

    // Générer une réponse
    generateResponse(message) {
        const responses = [
            `🤖 JARVIS (Thermal): Bonjour Jean-Luc ! Vous avez dit: "${message}"`,
            `🧠 JARVIS (Mémoire Thermique): Je traite votre demande "${message}" avec mes ${this.jarvisAgent.thermal_integration.neural_allocation.toLocaleString()} neurones dédiés.`,
            `🎯 JARVIS (Intégré): Message reçu "${message}". Utilisation de la mémoire thermique pour optimiser ma réponse.`,
            `🌡️ JARVIS (Thermal Agent): "${message}" - Traitement via interface thermique, QI boost +${this.jarvisAgent.thermal_integration.qi_boost}.`,
            `🚀 JARVIS (Activé): Jean-Luc, votre message "${message}" est traité par l'agent thermal intégré depuis ${this.jarvisAgent.app_path}.`
        ];
        
        return responses[Math.floor(Math.random() * responses.length)];
    }

    // Obtenir le statut
    getStatus() {
        return `📊 STATUT JARVIS THERMAL:
🆔 Agent ID: ${this.jarvisAgent.agent_id}
🎯 Type: ${this.jarvisAgent.agent_type}
📁 Source: ${this.jarvisAgent.app_path}
🌡️ Thermal: ${this.jarvisAgent.thermal_integration.status}
🧠 QI Boost: +${this.jarvisAgent.thermal_integration.qi_boost}
⚡ Actif: ${this.active ? 'OUI' : 'NON'}`;
    }

    // Obtenir info composants
    getComponentsInfo() {
        let info = '🔧 COMPOSANTS JARVIS:\n';
        for (const [name, component] of Object.entries(this.jarvisAgent.components)) {
            info += `\n📄 ${name}:`;
            info += `\n  📏 Taille: ${component.size} caractères`;
            info += `\n  🌐 Ports: ${component.ports ? component.ports.join(', ') : 'Aucun'}`;
            info += `\n  ✅ Fonctionnalités: `;
            const features = [];
            if (component.has_express) features.push('Express');
            if (component.has_socket) features.push('WebSocket');
            if (component.has_thermal) features.push('Thermal');
            if (component.has_deepseek) features.push('DeepSeek');
            if (component.has_brain) features.push('Brain');
            info += features.join(', ') || 'Aucune détectée';
        }
        return info;
    }

    // Obtenir info thermique
    getThermalInfo() {
        const qi = this.thermalMemory.neural_system.qi_level;
        const neurons = this.thermalMemory.neural_system.total_neurons;
        
        return `🌡️ MÉMOIRE THERMIQUE:
🧠 QI Total: ${qi}
🧬 Neurones totaux: ${neurons.toLocaleString()}
🎯 Agent JARVIS:
  🆔 ID: ${this.jarvisAgent.agent_id}
  🧠 QI Contribution: +${this.jarvisAgent.thermal_integration.qi_boost}
  🧬 Neurones dédiés: ${this.jarvisAgent.thermal_integration.neural_allocation.toLocaleString()}
  📊 Zones: ${this.jarvisAgent.thermal_integration.memory_zones.join(', ')}`;
    }

    // Mettre à jour l'activité thermique
    updateThermalActivity() {
        // Mettre à jour le timestamp d'activité
        this.jarvisAgent.thermal_integration.last_activity = Date.now();
        
        // Sauvegarder si nécessaire (optionnel pour éviter trop d'écritures)
        // fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(this.thermalMemory, null, 2));
    }

    // Interface interactive
    async startInteractiveMode() {
        this.activate();
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        const askQuestion = () => {
            if (!this.active) {
                rl.close();
                return;
            }
            
            rl.question('\n💬 Jean-Luc > ', (input) => {
                if (input.trim()) {
                    const response = this.processMessage(input.trim());
                    console.log(response);
                }
                askQuestion();
            });
        };
        
        askQuestion();
    }
}

async function main() {
    const jarvis = new JarvisThermalAgent();
    
    try {
        await jarvis.startInteractiveMode();
    } catch (error) {
        console.error('❌ Erreur:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = JarvisThermalAgent;
