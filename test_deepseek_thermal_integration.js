#!/usr/bin/env node

/**
 * 🧪 TEST INTÉGRATION DEEPSEEK R1 8B DANS MÉMOIRE THERMIQUE
 * Test de l'intégration directe dans le code
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');
const path = require('path');

class DeepSeekThermalIntegrationTest {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.serverPath = './cloned_agents/deepseek_r1_authentic_1749984237489/ollama_models/LOUNA-AI-COMPLET/deepseek-node-ui/server-louna.js';
        this.thermalMemory = null;
        
        console.log('🧪 TEST INTÉGRATION DEEPSEEK R1 8B THERMAL');
        console.log('==========================================');
    }

    // Test 1: Vérifier que le code a été modifié
    testCodeModification() {
        console.log('\n🔍 TEST 1: Modification du code serveur');
        console.log('=======================================');
        
        if (!fs.existsSync(this.serverPath)) {
            console.log('❌ Fichier serveur non trouvé');
            return false;
        }
        
        const serverCode = fs.readFileSync(this.serverPath, 'utf8');
        
        const checks = {
            'DeepSeekR1ThermalIntegration': serverCode.includes('DeepSeekR1ThermalIntegration'),
            'realThermalMemory': serverCode.includes('realThermalMemory'),
            'queryWithThermalContext': serverCode.includes('queryWithThermalContext'),
            'deepseek_agents': serverCode.includes('deepseek_agents'),
            'api/deepseek/query': serverCode.includes('/api/deepseek/query'),
            'thermal_memory_real_clones': serverCode.includes('thermal_memory_real_clones')
        };
        
        let passed = 0;
        for (const [check, result] of Object.entries(checks)) {
            const status = result ? '✅' : '❌';
            console.log(`${status} ${check}: ${result ? 'TROUVÉ' : 'MANQUANT'}`);
            if (result) passed++;
        }
        
        console.log(`\n📊 Résultat: ${passed}/${Object.keys(checks).length} vérifications réussies`);
        return passed === Object.keys(checks).length;
    }

    // Test 2: Vérifier la mémoire thermique
    testThermalMemoryAccess() {
        console.log('\n🌡️ TEST 2: Accès mémoire thermique');
        console.log('==================================');
        
        if (!fs.existsSync(this.thermalMemoryPath)) {
            console.log('❌ Mémoire thermique non trouvée');
            return false;
        }
        
        try {
            this.thermalMemory = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            console.log('✅ Mémoire thermique chargée');
            console.log(`🧠 QI système: ${this.thermalMemory.neural_system?.qi_level || 'Unknown'}`);
            console.log(`🧬 Neurones totaux: ${this.thermalMemory.neural_system?.total_neurons?.toLocaleString() || 'Unknown'}`);
            console.log(`🔥 Agents DeepSeek: ${Object.keys(this.thermalMemory.deepseek_agents || {}).length}`);
            
            return true;
        } catch (error) {
            console.log('❌ Erreur lecture mémoire thermique:', error.message);
            return false;
        }
    }

    // Test 3: Vérifier l'intégration DeepSeek
    testDeepSeekIntegration() {
        console.log('\n🤖 TEST 3: Intégration DeepSeek');
        console.log('===============================');
        
        if (!this.thermalMemory) {
            console.log('❌ Mémoire thermique non chargée');
            return false;
        }
        
        const deepseekAgents = this.thermalMemory.deepseek_agents || {};
        const agentCount = Object.keys(deepseekAgents).length;
        
        console.log(`🔢 Agents DeepSeek trouvés: ${agentCount}`);
        
        if (agentCount === 0) {
            console.log('⚠️ Aucun agent DeepSeek trouvé dans la mémoire thermique');
            return false;
        }
        
        // Analyser le dernier agent
        const agentIds = Object.keys(deepseekAgents);
        const latestAgentId = agentIds[agentIds.length - 1];
        const latestAgent = deepseekAgents[latestAgentId];
        
        console.log(`🆔 Dernier agent: ${latestAgentId}`);
        console.log(`🎯 Modèle: ${latestAgent.model || 'Unknown'}`);
        console.log(`🧠 QI boost: +${latestAgent.thermal_integration?.qi_boost || 0}`);
        console.log(`🧬 Neurones dédiés: ${latestAgent.thermal_integration?.neural_allocation?.toLocaleString() || 'Unknown'}`);
        console.log(`📊 Zones mémoire: ${latestAgent.thermal_integration?.memory_zones?.length || 0}`);
        console.log(`✅ Statut: ${latestAgent.thermal_integration?.status || 'Unknown'}`);
        
        return true;
    }

    // Test 4: Vérifier l'impact sur le QI
    testQIImpact() {
        console.log('\n🧠 TEST 4: Impact sur le QI');
        console.log('============================');
        
        if (!this.thermalMemory || !this.thermalMemory.neural_system) {
            console.log('❌ Système neural non trouvé');
            return false;
        }
        
        const qiComponents = this.thermalMemory.neural_system.qi_components || {};
        const totalQI = this.thermalMemory.neural_system.qi_level || 0;
        
        console.log(`🧠 QI total: ${totalQI}`);
        console.log('\n📊 Composants QI:');
        
        let deepseekContribution = 0;
        for (const [component, value] of Object.entries(qiComponents)) {
            console.log(`  ${component}: +${value}`);
            if (component.includes('deepseek')) {
                deepseekContribution += value;
            }
        }
        
        console.log(`\n🤖 Contribution DeepSeek totale: +${deepseekContribution}`);
        
        return deepseekContribution > 0;
    }

    // Test 5: Simuler une requête API
    async testAPISimulation() {
        console.log('\n🌐 TEST 5: Simulation API');
        console.log('=========================');
        
        // Simuler la logique de l'API sans faire de vraie requête HTTP
        const mockPrompt = "Bonjour DeepSeek, utilises-tu la mémoire thermique ?";
        
        console.log(`📝 Prompt test: ${mockPrompt}`);
        
        // Simuler la construction du contexte thermal
        if (!this.thermalMemory) {
            console.log('❌ Pas de mémoire thermique pour le contexte');
            return false;
        }
        
        const neural = this.thermalMemory.neural_system;
        const agents = this.thermalMemory.deepseek_agents || {};
        
        const mockContext = `QI Système: ${neural?.qi_level || 'Unknown'}
Neurones Totaux: ${neural?.total_neurons?.toLocaleString() || 'Unknown'}
Neurones Actifs: ${neural?.active_neurons?.toLocaleString() || 'Unknown'}
Agents DeepSeek Intégrés: ${Object.keys(agents).length}`;

        console.log('\n🧠 Contexte thermal simulé:');
        console.log(mockContext);
        
        const mockFullPrompt = `Tu es DeepSeek R1 8B intégré dans la mémoire thermique de Jean-Luc.

CONTEXTE MÉMOIRE THERMIQUE:
${mockContext}

INSTRUCTION: Réponds en utilisant ce contexte de mémoire thermique.

QUESTION: ${mockPrompt}`;

        console.log('\n📤 Prompt complet construit (aperçu):');
        console.log(mockFullPrompt.substring(0, 200) + '...');
        
        console.log('\n✅ Simulation API réussie - Le contexte thermal est correctement intégré');
        return true;
    }

    // Test 6: Vérifier les backups
    testBackups() {
        console.log('\n💾 TEST 6: Vérification backups');
        console.log('===============================');
        
        const currentDir = '.';
        const files = fs.readdirSync(currentDir);
        
        const backupFiles = files.filter(file => 
            file.includes('thermal_memory') && 
            file.includes('backup') && 
            (file.includes('deepseek') || file.includes('integration'))
        );
        
        console.log(`📁 Backups trouvés: ${backupFiles.length}`);
        
        for (const backup of backupFiles) {
            const stats = fs.statSync(backup);
            console.log(`  📄 ${backup} (${(stats.size / 1024 / 1024).toFixed(1)} MB)`);
        }
        
        return backupFiles.length > 0;
    }

    // Exécuter tous les tests
    async runAllTests() {
        console.log('🚀 DÉMARRAGE TESTS INTÉGRATION DEEPSEEK R1 8B');
        console.log('==============================================');
        
        const tests = [
            { name: 'Code Modification', test: () => this.testCodeModification() },
            { name: 'Thermal Memory Access', test: () => this.testThermalMemoryAccess() },
            { name: 'DeepSeek Integration', test: () => this.testDeepSeekIntegration() },
            { name: 'QI Impact', test: () => this.testQIImpact() },
            { name: 'API Simulation', test: () => this.testAPISimulation() },
            { name: 'Backups', test: () => this.testBackups() }
        ];
        
        const results = {};
        let passedTests = 0;
        
        for (const { name, test } of tests) {
            try {
                const result = await test();
                results[name] = result;
                if (result) passedTests++;
            } catch (error) {
                console.error(`❌ Erreur test ${name}:`, error.message);
                results[name] = false;
            }
        }
        
        // Résumé final
        console.log('\n📊 RÉSUMÉ TESTS');
        console.log('===============');
        
        for (const [testName, passed] of Object.entries(results)) {
            const status = passed ? '✅ PASSÉ' : '❌ ÉCHEC';
            console.log(`${testName}: ${status}`);
        }
        
        console.log(`\n🎯 RÉSULTAT: ${passedTests}/${tests.length} tests réussis`);
        
        if (passedTests === tests.length) {
            console.log('🏆 DEEPSEEK R1 8B PARFAITEMENT INTÉGRÉ DANS LA MÉMOIRE THERMIQUE !');
        } else if (passedTests >= tests.length * 0.8) {
            console.log('✅ DeepSeek R1 8B majoritairement intégré');
        } else {
            console.log('⚠️ Intégration DeepSeek R1 8B partielle');
        }
        
        return results;
    }
}

async function main() {
    const tester = new DeepSeekThermalIntegrationTest();
    await tester.runAllTests();
}

if (require.main === module) {
    main();
}

module.exports = DeepSeekThermalIntegrationTest;
