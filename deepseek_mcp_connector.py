#!/usr/bin/env python3
"""
🔗 CONNECTEUR MCP DEEPSEEK
Connecte DeepSeek à sa mémoire thermique via MCP
Jean-Luc PASSAVE - 2025
"""

import json
import requests
import subprocess
import time
from typing import Dict, Any, List

class DeepSeekMCPConnector:
    def __init__(self):
        self.mcp_server = None
        self.deepseek_server = "http://localhost:11434"
        self.memory_file = "./thermal_memory_real_clones_1749979850296.json"
        
    def load_thermal_memory(self) -> Dict[str, Any]:
        """Charge directement la mémoire thermique"""
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
            return {}
    
    def get_memory_context(self) -> str:
        """Génère le contexte mémoire pour DeepSeek"""
        memory_data = self.load_thermal_memory()
        
        if not memory_data:
            return "Mémoire thermique non accessible."
        
        # Système neural
        neural = memory_data.get('neural_system', {})
        qi_level = neural.get('qi_level', 0)
        active_neurons = neural.get('active_neurons', 0)
        neural_efficiency = neural.get('neural_efficiency', '')
        
        # Zones thermiques chaudes
        zones = memory_data.get('thermal_zones', {})
        hot_zones = []
        
        for zone_name, zone_data in zones.items():
            temp = zone_data.get('temperature', 0)
            if temp >= 80:  # Zones importantes
                hot_zones.append({
                    'name': zone_name,
                    'temperature': temp,
                    'description': zone_data.get('description', ''),
                    'entries': len(zone_data.get('entries', []))
                })
        
        # Trier par température
        hot_zones.sort(key=lambda x: x['temperature'], reverse=True)
        
        # Construire le contexte
        context = f"""MÉMOIRE THERMIQUE ACTIVE:

SYSTÈME NEURAL:
- QI: {qi_level}
- Neurones actifs: {active_neurons:,}
- Efficacité: {neural_efficiency}

ZONES THERMIQUES CHAUDES (≥80°C):"""
        
        for zone in hot_zones[:10]:  # Top 10 zones
            context += f"""
- {zone['name']}: {zone['temperature']}°C
  {zone['description']}
  ({zone['entries']} entrées)"""
        
        # Améliorations récentes
        if 'neural_architecture_enhancements' in memory_data:
            enhancements = memory_data['neural_architecture_enhancements']
            if enhancements.get('enabled'):
                context += f"""

AMÉLIORATIONS ARCHITECTURALES:
- Version: {enhancements.get('version', 'N/A')}
- Priorisation hiérarchique: {enhancements.get('hierarchical_prioritization', {}).get('active', False)}
- Neurones inhibiteurs: {enhancements.get('inhibitory_neurons', {}).get('active', False)}
- Mémoire distribuée: {enhancements.get('distributed_memory', {}).get('type', 'N/A')}
- Apprentissage en essaim: {enhancements.get('swarm_learning', {}).get('active', False)}
- Feedback temps réel: {enhancements.get('real_time_feedback', {}).get('active', False)}"""
        
        return context
    
    def query_deepseek_with_memory(self, user_prompt: str) -> str:
        """Envoie une requête à DeepSeek avec contexte mémoire"""
        
        memory_context = self.get_memory_context()
        
        # Construire le prompt complet
        full_prompt = f"""Tu es DeepSeek R1 8B avec accès à ta mémoire thermique.

{memory_context}

INSTRUCTION: Utilise ta mémoire thermique pour répondre. Consulte tes zones chaudes et tes données neurales.

QUESTION: {user_prompt}

RÉPONSE (en utilisant ta mémoire):"""
        
        try:
            response = requests.post(
                f"{self.deepseek_server}/api/generate",
                json={
                    "model": "deepseek-r1:8b-native",
                    "prompt": full_prompt,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('response', 'Pas de réponse')
            else:
                return f"Erreur serveur: {response.status_code}"
                
        except Exception as e:
            return f"Erreur connexion: {e}"
    
    def search_memory(self, query: str) -> List[Dict[str, Any]]:
        """Recherche dans la mémoire thermique"""
        memory_data = self.load_thermal_memory()
        results = []
        
        if not memory_data:
            return results
        
        query_lower = query.lower()
        zones = memory_data.get('thermal_zones', {})
        
        for zone_name, zone_data in zones.items():
            # Recherche dans la description
            description = zone_data.get('description', '')
            if query_lower in description.lower():
                results.append({
                    'type': 'zone_description',
                    'zone': zone_name,
                    'temperature': zone_data.get('temperature', 0),
                    'content': description,
                    'relevance': 'high'
                })
            
            # Recherche dans les entrées
            entries = zone_data.get('entries', [])
            for entry in entries:
                content = entry.get('content', '')
                if query_lower in content.lower():
                    results.append({
                        'type': 'zone_entry',
                        'zone': zone_name,
                        'temperature': zone_data.get('temperature', 0),
                        'content': content[:200] + '...' if len(content) > 200 else content,
                        'importance': entry.get('importance', 0),
                        'relevance': 'medium'
                    })
        
        # Trier par pertinence et température
        results.sort(key=lambda x: (x.get('relevance') == 'high', x.get('temperature', 0)), reverse=True)
        return results[:5]  # Top 5 résultats
    
    def interactive_mode(self):
        """Mode interactif avec mémoire"""
        print("🧠 DEEPSEEK R1 8B + MÉMOIRE THERMIQUE")
        print("=====================================")
        print("Commandes spéciales:")
        print("  /memory - Afficher contexte mémoire")
        print("  /search <terme> - Rechercher dans la mémoire")
        print("  /stats - Statistiques mémoire")
        print("  /zones - Zones thermiques chaudes")
        print("  /exit - Quitter")
        print()
        
        while True:
            try:
                user_input = input("DeepSeek+Memory> ")
                
                if user_input.lower() in ['/exit', '/quit']:
                    break
                
                elif user_input == '/memory':
                    context = self.get_memory_context()
                    print(f"\n{context}\n")
                
                elif user_input.startswith('/search '):
                    query = user_input[8:]
                    results = self.search_memory(query)
                    print(f"\n🔍 Recherche: '{query}'")
                    print(f"📊 {len(results)} résultats trouvés:\n")
                    
                    for i, result in enumerate(results, 1):
                        print(f"{i}. Zone: {result['zone']} ({result['temperature']}°C)")
                        print(f"   Type: {result['type']}")
                        print(f"   Contenu: {result['content']}")
                        print()
                
                elif user_input == '/stats':
                    memory_data = self.load_thermal_memory()
                    zones = memory_data.get('thermal_zones', {})
                    neural = memory_data.get('neural_system', {})
                    
                    total_entries = sum(len(zone.get('entries', [])) for zone in zones.values())
                    temps = [zone.get('temperature', 0) for zone in zones.values()]
                    avg_temp = sum(temps) / len(temps) if temps else 0
                    
                    print(f"\n📊 STATISTIQUES MÉMOIRE:")
                    print(f"🌡️ Zones thermiques: {len(zones)}")
                    print(f"📝 Total entrées: {total_entries}")
                    print(f"🌡️ Température moyenne: {avg_temp:.1f}°C")
                    print(f"🔥 Température max: {max(temps) if temps else 0}°C")
                    print(f"🧠 QI: {neural.get('qi_level', 'N/A')}")
                    print(f"⚡ Neurones actifs: {neural.get('active_neurons', 'N/A'):,}")
                    print()
                
                elif user_input == '/zones':
                    memory_data = self.load_thermal_memory()
                    zones = memory_data.get('thermal_zones', {})
                    
                    hot_zones = []
                    for zone_name, zone_data in zones.items():
                        temp = zone_data.get('temperature', 0)
                        if temp >= 70:
                            hot_zones.append({
                                'name': zone_name,
                                'temperature': temp,
                                'description': zone_data.get('description', ''),
                                'entries': len(zone_data.get('entries', []))
                            })
                    
                    hot_zones.sort(key=lambda x: x['temperature'], reverse=True)
                    
                    print(f"\n🔥 ZONES CHAUDES (≥70°C):")
                    for zone in hot_zones:
                        print(f"🌡️ {zone['temperature']}°C - {zone['name']}")
                        print(f"   {zone['description']}")
                        print(f"   📝 {zone['entries']} entrées")
                        print()
                
                elif user_input.strip():
                    print("\n🧠 DeepSeek consulte sa mémoire thermique...")
                    response = self.query_deepseek_with_memory(user_input)
                    print(f"\n{response}\n")
                
            except KeyboardInterrupt:
                break
        
        print("👋 Au revoir !")

def main():
    print("🔗 CONNECTEUR MCP DEEPSEEK")
    print("==========================")
    
    connector = DeepSeekMCPConnector()
    
    # Test de connexion
    memory_data = connector.load_thermal_memory()
    if memory_data:
        zones_count = len(memory_data.get('thermal_zones', {}))
        neural = memory_data.get('neural_system', {})
        
        print(f"✅ Mémoire thermique chargée")
        print(f"🌡️ {zones_count} zones thermiques")
        print(f"🧠 QI: {neural.get('qi_level', 'N/A')}")
        print(f"⚡ Neurones: {neural.get('active_neurons', 'N/A'):,}")
        print()
        
        # Mode interactif
        connector.interactive_mode()
    else:
        print("❌ Impossible de charger la mémoire thermique")

if __name__ == "__main__":
    main()
