#!/usr/bin/env node

/**
 * 🔒 SYSTÈME MÉMOIRE THERMIQUE SÉCURISÉ
 * 
 * Protection et garde-fous pour la mémoire thermique
 * Connexion directe à Ollama pour Jean-Luc
 * 
 * <PERSON><PERSON><PERSON> PASSAVE - 2025
 */

const fs = require('fs');
const { spawn } = require('child_process');
const readline = require('readline');

class SecureThermalMemorySystem {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.securityConfig = {
            max_qi_level: 1000,
            max_neurons: 100000000000, // 100 milliards max
            max_zones: 20,
            allowed_operations: ['read', 'limited_write', 'query'],
            blocked_operations: ['delete_zones', 'modify_core', 'external_access'],
            jean_luc_permissions: 'FULL_ADMIN'
        };
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    async initialize() {
        console.log('🔒 SYSTÈME MÉMOIRE THERMIQUE SÉCURISÉ');
        console.log('=====================================');
        console.log('🛡️ Initialisation des protections...');
        
        try {
            // Vérifier l'intégrité de la mémoire thermique
            const integrityCheck = await this.checkMemoryIntegrity();
            
            if (!integrityCheck.safe) {
                console.log('⚠️ ALERTE SÉCURITÉ: Mémoire thermique compromise');
                return false;
            }
            
            // Appliquer les protections
            await this.applySecurityMeasures();
            
            console.log('✅ Système sécurisé initialisé');
            console.log(`🔒 Niveau sécurité: MAXIMUM`);
            console.log(`👤 Permissions Jean-Luc: ${this.securityConfig.jean_luc_permissions}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation sécurité:', error.message);
            return false;
        }
    }

    async checkMemoryIntegrity() {
        console.log('🔍 Vérification intégrité mémoire thermique...');
        
        const integrity = {
            safe: true,
            issues: [],
            qi_level: 0,
            neuron_count: 0,
            zone_count: 0
        };
        
        try {
            if (!fs.existsSync(this.thermalMemoryPath)) {
                integrity.safe = false;
                integrity.issues.push('Fichier mémoire thermique manquant');
                return integrity;
            }
            
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            // Vérifier les limites de sécurité
            integrity.qi_level = thermalData.neural_system?.qi_level || 0;
            integrity.neuron_count = thermalData.neural_system?.total_neurons || 0;
            integrity.zone_count = Object.keys(thermalData.thermal_zones || {}).length;
            
            if (integrity.qi_level > this.securityConfig.max_qi_level) {
                integrity.issues.push(`QI trop élevé: ${integrity.qi_level} > ${this.securityConfig.max_qi_level}`);
            }
            
            if (integrity.neuron_count > this.securityConfig.max_neurons) {
                integrity.issues.push(`Trop de neurones: ${integrity.neuron_count} > ${this.securityConfig.max_neurons}`);
            }
            
            if (integrity.zone_count > this.securityConfig.max_zones) {
                integrity.issues.push(`Trop de zones: ${integrity.zone_count} > ${this.securityConfig.max_zones}`);
            }
            
            // Vérifier les zones critiques
            const criticalZones = ['zone_deepseek_r1_authentic', 'zone1_working', 'zone2_episodic'];
            criticalZones.forEach(zoneName => {
                if (!thermalData.thermal_zones[zoneName]) {
                    integrity.issues.push(`Zone critique manquante: ${zoneName}`);
                }
            });
            
            integrity.safe = integrity.issues.length === 0;
            
            console.log(`✅ QI niveau: ${integrity.qi_level} (limite: ${this.securityConfig.max_qi_level})`);
            console.log(`✅ Neurones: ${integrity.neuron_count.toLocaleString()} (limite: ${this.securityConfig.max_neurons.toLocaleString()})`);
            console.log(`✅ Zones: ${integrity.zone_count} (limite: ${this.securityConfig.max_zones})`);
            console.log(`${integrity.safe ? '✅' : '❌'} Intégrité: ${integrity.safe ? 'SÉCURISÉE' : 'COMPROMISE'}`);
            
            if (integrity.issues.length > 0) {
                console.log('⚠️ Problèmes détectés:');
                integrity.issues.forEach(issue => console.log(`  - ${issue}`));
            }
            
        } catch (error) {
            integrity.safe = false;
            integrity.issues.push(`Erreur lecture: ${error.message}`);
        }
        
        return integrity;
    }

    async applySecurityMeasures() {
        console.log('🛡️ Application des mesures de sécurité...');
        
        try {
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            // Ajouter un système de sécurité dans la mémoire thermique
            if (!thermalData.security_system) {
                thermalData.security_system = {
                    enabled: true,
                    level: 'MAXIMUM',
                    admin_user: 'Jean-Luc PASSAVE',
                    creation_timestamp: Date.now(),
                    protections: {
                        qi_limit_enforced: true,
                        neuron_limit_enforced: true,
                        zone_limit_enforced: true,
                        external_access_blocked: true,
                        unauthorized_modifications_blocked: true
                    },
                    access_log: []
                };
            }
            
            // Créer une zone de sécurité
            if (!thermalData.thermal_zones.zone_security_system) {
                thermalData.thermal_zones.zone_security_system = {
                    temperature: 35.0, // Température basse = sécurité
                    description: 'Zone système de sécurité - Accès restreint Jean-Luc',
                    entries: [{
                        id: `security_init_${Date.now()}`,
                        content: 'SYSTÈME SÉCURITÉ ACTIVÉ - Protection mémoire thermique contre accès non autorisés. Seul Jean-Luc PASSAVE a les permissions administrateur.',
                        importance: 1.0,
                        timestamp: Math.floor(Date.now() / 1000),
                        synaptic_strength: 1.0,
                        temperature: 35.0,
                        zone: 'zone_security_system',
                        source: 'security_system',
                        type: 'security_log',
                        access_level: 'ADMIN_ONLY'
                    }],
                    access_level: 'ADMIN_ONLY',
                    protected: true
                };
            }
            
            // Sauvegarder avec protections
            const backupPath = `${this.thermalMemoryPath}.security_backup_${Date.now()}`;
            fs.copyFileSync(this.thermalMemoryPath, backupPath);
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
            
            console.log('✅ Protections appliquées');
            console.log(`💾 Sauvegarde sécurité: ${backupPath}`);
            
        } catch (error) {
            console.error('❌ Erreur application sécurité:', error.message);
            throw error;
        }
    }

    async startDirectOllamaConnection() {
        console.log('\n🔗 CONNEXION DIRECTE OLLAMA');
        console.log('=====================================');
        console.log('🎯 Interface directe Jean-Luc ↔ Ollama');
        console.log('🔒 Mémoire thermique protégée en arrière-plan');
        console.log('=====================================\n');
        
        console.log('💡 Commandes disponibles:');
        console.log('   /models - Lister les modèles disponibles');
        console.log('   /status - État du système');
        console.log('   /security - Vérifier la sécurité');
        console.log('   /memory - État mémoire thermique');
        console.log('   /exit - Quitter');
        console.log('   Ou tapez directement vos questions pour DeepSeek R1 8B');
        console.log('=====================================\n');
        
        this.promptDirectOllama();
    }

    promptDirectOllama() {
        this.rl.question('🔗 Jean-Luc → Ollama > ', async (input) => {
            const command = input.trim();
            
            if (command === '/exit') {
                console.log('👋 Connexion fermée. Mémoire thermique sécurisée.');
                this.rl.close();
                return;
            }
            
            if (command === '/models') {
                await this.listOllamaModels();
                this.promptDirectOllama();
                return;
            }
            
            if (command === '/status') {
                await this.showSystemStatus();
                this.promptDirectOllama();
                return;
            }
            
            if (command === '/security') {
                await this.showSecurityStatus();
                this.promptDirectOllama();
                return;
            }
            
            if (command === '/memory') {
                await this.showMemoryStatus();
                this.promptDirectOllama();
                return;
            }
            
            if (command.length === 0) {
                this.promptDirectOllama();
                return;
            }
            
            // Connexion directe à DeepSeek R1 8B
            await this.directOllamaQuery(command);
            this.promptDirectOllama();
        });
    }

    async directOllamaQuery(question) {
        console.log('\n🤖 DeepSeek R1 8B (connexion directe):');
        console.log('=====================================');
        
        // Log sécurisé de l'interaction
        await this.logSecureInteraction(question);
        
        try {
            const response = await this.queryOllamaDirect(question);
            
            if (response) {
                console.log(response);
            } else {
                console.log('❌ Pas de réponse de DeepSeek R1 8B');
            }
            
        } catch (error) {
            console.error('❌ Erreur connexion Ollama:', error.message);
        }
        
        console.log('=====================================');
    }

    async queryOllamaDirect(question) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', question]);
            
            let output = '';
            let errorOutput = '';
            
            ollama.stdout.on('data', (data) => {
                const chunk = data.toString();
                output += chunk;
                // Affichage en temps réel
                process.stdout.write(chunk);
            });
            
            ollama.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            // Timeout après 120 secondes
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, 120000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                if (code === 0) {
                    resolve(output.trim());
                } else {
                    resolve(null);
                }
            });
        });
    }

    async logSecureInteraction(question) {
        try {
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            // Ajouter au log sécurisé
            if (thermalData.security_system) {
                thermalData.security_system.access_log.push({
                    timestamp: Date.now(),
                    user: 'Jean-Luc PASSAVE',
                    action: 'direct_ollama_query',
                    question_preview: question.substring(0, 50),
                    authorized: true
                });
                
                // Garder seulement les 100 derniers logs
                if (thermalData.security_system.access_log.length > 100) {
                    thermalData.security_system.access_log = 
                        thermalData.security_system.access_log.slice(-100);
                }
                
                fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
            }
            
        } catch (error) {
            // Log silencieux en cas d'erreur
        }
    }

    async listOllamaModels() {
        console.log('\n📋 MODÈLES OLLAMA DISPONIBLES');
        console.log('=====================================');
        
        try {
            const { exec } = require('child_process');
            
            exec('ollama list', (error, stdout, stderr) => {
                if (error) {
                    console.log('❌ Erreur accès Ollama');
                } else {
                    console.log(stdout);
                }
            });
            
        } catch (error) {
            console.log('❌ Erreur liste modèles:', error.message);
        }
    }

    async showSystemStatus() {
        console.log('\n📊 ÉTAT DU SYSTÈME');
        console.log('=====================================');
        
        const integrity = await this.checkMemoryIntegrity();
        
        console.log(`🔒 Sécurité: ${integrity.safe ? 'ACTIVE' : 'COMPROMISE'}`);
        console.log(`🧠 QI système: ${integrity.qi_level}`);
        console.log(`🧬 Neurones: ${integrity.neuron_count.toLocaleString()}`);
        console.log(`🔥 Zones thermiques: ${integrity.zone_count}`);
    }

    async showSecurityStatus() {
        console.log('\n🛡️ ÉTAT SÉCURITÉ');
        console.log('=====================================');
        
        try {
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            const security = thermalData.security_system;
            
            if (security) {
                console.log(`🔒 Niveau: ${security.level}`);
                console.log(`👤 Admin: ${security.admin_user}`);
                console.log(`✅ Protections actives: ${Object.values(security.protections).filter(p => p).length}`);
                console.log(`📝 Logs récents: ${security.access_log.length}`);
            } else {
                console.log('⚠️ Système sécurité non initialisé');
            }
            
        } catch (error) {
            console.log('❌ Erreur lecture sécurité:', error.message);
        }
    }

    async showMemoryStatus() {
        console.log('\n🧠 ÉTAT MÉMOIRE THERMIQUE');
        console.log('=====================================');
        
        const integrity = await this.checkMemoryIntegrity();
        
        console.log(`📊 Intégrité: ${integrity.safe ? 'SÉCURISÉE' : 'COMPROMISE'}`);
        console.log(`🔥 Zones actives: ${integrity.zone_count}`);
        console.log(`🧠 QI niveau: ${integrity.qi_level}`);
        console.log(`🧬 Neurones: ${integrity.neuron_count.toLocaleString()}`);
        
        if (integrity.issues.length > 0) {
            console.log('\n⚠️ Problèmes:');
            integrity.issues.forEach(issue => console.log(`  - ${issue}`));
        }
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🔒 SYSTÈME MÉMOIRE THERMIQUE SÉCURISÉ');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Protection mémoire + Connexion directe Ollama');
    
    const secureSystem = new SecureThermalMemorySystem();
    
    const initialized = await secureSystem.initialize();
    if (initialized) {
        await secureSystem.startDirectOllamaConnection();
    } else {
        console.log('❌ Impossible d\'initialiser le système sécurisé');
    }
}

if (require.main === module) {
    main();
}

module.exports = SecureThermalMemorySystem;
