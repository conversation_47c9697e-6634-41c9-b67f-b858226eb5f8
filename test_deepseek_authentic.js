#!/usr/bin/env node

/**
 * 🧪 TEST AUTHENTIQUE DEEPSEEK R1 8B
 * 
 * Test réel de votre DeepSeek R1 8B - PAS DE SIMULATION
 * Vérification authentique des capacités
 * 
 * Jean-Luc PASSAVE - 2025
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');

class AuthenticDeepSeekTester {
    constructor() {
        this.testResults = [];
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
    }

    async runAuthenticTests() {
        console.log('🧪 TEST AUTHENTIQUE DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('⚠️  TESTS RÉELS - PAS DE SIMULATION');
        console.log('🎯 Vérification des capacités authentiques');
        
        try {
            // Vérifier que DeepSeek est disponible
            console.log('\n🔍 Vérification de la disponibilité...');
            const isAvailable = await this.checkDeepSeekAvailability();
            
            if (!isAvailable) {
                console.log('❌ DeepSeek R1 8B non disponible');
                console.log('💡 Vérifiez que Ollama est démarré et que le modèle est installé');
                return false;
            }
            
            console.log('✅ DeepSeek R1 8B disponible et prêt');
            
            // Tests authentiques
            console.log('\n🧪 DÉBUT DES TESTS AUTHENTIQUES');
            console.log('=====================================');
            
            // Test 1: Calcul simple
            await this.testSimpleCalculation();
            
            // Test 2: Génération de code
            await this.testCodeGeneration();
            
            // Test 3: Raisonnement logique
            await this.testLogicalReasoning();
            
            // Test 4: Conversation
            await this.testConversation();
            
            // Test 5: Analyse de la mémoire thermique
            await this.testThermalMemoryIntegration();
            
            // Générer le rapport final
            const report = this.generateTestReport();
            
            console.log('\n📊 RÉSULTATS FINAUX');
            console.log('=====================================');
            console.log(`✅ Tests réussis: ${report.passed_tests}/${report.total_tests}`);
            console.log(`📈 Taux de succès: ${report.success_rate.toFixed(1)}%`);
            console.log(`🔥 Agent authentique: ${report.is_authentic ? 'OUI' : 'NON'}`);
            console.log(`⏱️ Temps total: ${report.total_duration.toFixed(1)}s`);
            
            if (report.is_authentic) {
                console.log('\n🎉 AGENT DEEPSEEK R1 8B AUTHENTIQUE CONFIRMÉ !');
                console.log('✅ Votre agent fonctionne vraiment');
                console.log('✅ Intégration mémoire thermique réussie');
                console.log('✅ Capacités vérifiées');
            } else {
                console.log('\n❌ PROBLÈME DÉTECTÉ');
                console.log('⚠️ L\'agent ne répond pas comme attendu');
                console.log('💡 Vérifiez la configuration');
            }
            
            return report;
            
        } catch (error) {
            console.error('\n❌ ERREUR LORS DES TESTS');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async checkDeepSeekAvailability() {
        return new Promise((resolve) => {
            exec('ollama list', (error, stdout, stderr) => {
                if (error) {
                    console.log('❌ Ollama non accessible');
                    resolve(false);
                    return;
                }
                
                const hasDeepSeek = stdout.includes('deepseek-r1:8b');
                if (hasDeepSeek) {
                    console.log('✅ DeepSeek R1 8B trouvé dans Ollama');
                    resolve(true);
                } else {
                    console.log('❌ DeepSeek R1 8B non trouvé');
                    resolve(false);
                }
            });
        });
    }

    async testSimpleCalculation() {
        console.log('\n🧮 Test 1: Calcul simple');
        console.log('Question: "Combien font 15 + 27 ?"');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek('Combien font 15 + 27 ? Réponds juste avec le nombre.');
        const duration = (Date.now() - startTime) / 1000;
        
        if (response) {
            console.log(`📝 Réponse: "${response.substring(0, 100)}..."`);
            
            // Vérifier si la réponse contient 42
            const contains42 = response.includes('42');
            const isCorrect = contains42;
            
            this.testResults.push({
                test: 'Calcul simple',
                question: '15 + 27',
                response: response.substring(0, 200),
                correct: isCorrect,
                duration: duration,
                expected: '42'
            });
            
            console.log(`${isCorrect ? '✅' : '❌'} Résultat: ${isCorrect ? 'CORRECT' : 'INCORRECT'}`);
            console.log(`⏱️ Temps: ${duration.toFixed(1)}s`);
        } else {
            console.log('❌ Pas de réponse obtenue');
            this.testResults.push({
                test: 'Calcul simple',
                correct: false,
                duration: duration,
                error: 'No response'
            });
        }
    }

    async testCodeGeneration() {
        console.log('\n💻 Test 2: Génération de code');
        console.log('Question: "Écris une fonction Python qui calcule la factorielle"');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek('Écris une fonction Python simple qui calcule la factorielle d\'un nombre. Juste le code, pas d\'explication.');
        const duration = (Date.now() - startTime) / 1000;
        
        if (response) {
            console.log(`📝 Réponse: "${response.substring(0, 150)}..."`);
            
            // Vérifier si c'est du code Python valide
            const hasDefKeyword = response.includes('def');
            const hasFactorial = response.toLowerCase().includes('factorial') || response.includes('fact');
            const hasPython = hasDefKeyword && (hasFactorial || response.includes('*') || response.includes('return'));
            
            this.testResults.push({
                test: 'Génération de code',
                question: 'Fonction factorielle Python',
                response: response.substring(0, 300),
                correct: hasPython,
                duration: duration,
                criteria: { hasDefKeyword, hasFactorial, hasPython }
            });
            
            console.log(`${hasPython ? '✅' : '❌'} Résultat: ${hasPython ? 'CODE PYTHON VALIDE' : 'PAS DE CODE VALIDE'}`);
            console.log(`⏱️ Temps: ${duration.toFixed(1)}s`);
        } else {
            console.log('❌ Pas de réponse obtenue');
            this.testResults.push({
                test: 'Génération de code',
                correct: false,
                duration: duration,
                error: 'No response'
            });
        }
    }

    async testLogicalReasoning() {
        console.log('\n🧠 Test 3: Raisonnement logique');
        console.log('Question: "Si tous les chats sont des mammifères et Félix est un chat, que peut-on dire de Félix ?"');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek('Si tous les chats sont des mammifères et Félix est un chat, que peut-on dire de Félix ? Réponds en une phrase.');
        const duration = (Date.now() - startTime) / 1000;
        
        if (response) {
            console.log(`📝 Réponse: "${response.substring(0, 150)}..."`);
            
            // Vérifier le raisonnement logique
            const mentionsMammifere = response.toLowerCase().includes('mammifère') || response.toLowerCase().includes('mammal');
            const mentionsFelix = response.toLowerCase().includes('félix') || response.toLowerCase().includes('felix');
            const isLogical = mentionsMammifere && mentionsFelix;
            
            this.testResults.push({
                test: 'Raisonnement logique',
                question: 'Syllogisme chat-mammifère',
                response: response.substring(0, 200),
                correct: isLogical,
                duration: duration,
                criteria: { mentionsMammifere, mentionsFelix, isLogical }
            });
            
            console.log(`${isLogical ? '✅' : '❌'} Résultat: ${isLogical ? 'RAISONNEMENT CORRECT' : 'RAISONNEMENT INCORRECT'}`);
            console.log(`⏱️ Temps: ${duration.toFixed(1)}s`);
        } else {
            console.log('❌ Pas de réponse obtenue');
            this.testResults.push({
                test: 'Raisonnement logique',
                correct: false,
                duration: duration,
                error: 'No response'
            });
        }
    }

    async testConversation() {
        console.log('\n💬 Test 4: Conversation');
        console.log('Question: "Bonjour, comment allez-vous ?"');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek('Bonjour, comment allez-vous ? Répondez poliment en français.');
        const duration = (Date.now() - startTime) / 1000;
        
        if (response) {
            console.log(`📝 Réponse: "${response.substring(0, 150)}..."`);
            
            // Vérifier la politesse et la cohérence
            const isPolite = response.toLowerCase().includes('bonjour') || 
                           response.toLowerCase().includes('bien') || 
                           response.toLowerCase().includes('merci') ||
                           response.toLowerCase().includes('salut');
            const isFrench = response.length > 10; // Réponse substantielle
            const isConversational = isPolite && isFrench;
            
            this.testResults.push({
                test: 'Conversation',
                question: 'Salutation polie',
                response: response.substring(0, 200),
                correct: isConversational,
                duration: duration,
                criteria: { isPolite, isFrench, isConversational }
            });
            
            console.log(`${isConversational ? '✅' : '❌'} Résultat: ${isConversational ? 'CONVERSATION NATURELLE' : 'RÉPONSE INADÉQUATE'}`);
            console.log(`⏱️ Temps: ${duration.toFixed(1)}s`);
        } else {
            console.log('❌ Pas de réponse obtenue');
            this.testResults.push({
                test: 'Conversation',
                correct: false,
                duration: duration,
                error: 'No response'
            });
        }
    }

    async testThermalMemoryIntegration() {
        console.log('\n🔥 Test 5: Intégration mémoire thermique');
        console.log('Vérification de l\'intégration dans votre mémoire thermique...');
        
        try {
            if (fs.existsSync(this.thermalMemoryPath)) {
                const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
                
                // Vérifier la zone DeepSeek
                const hasDeepSeekZone = thermalData.thermal_zones && 
                                      thermalData.thermal_zones.zone_deepseek_r1_authentic;
                
                // Vérifier l'augmentation du QI
                const currentQI = thermalData.neural_system?.qi_level || 0;
                const hasQIBoost = currentQI > 700; // Devrait être ~711
                
                // Vérifier l'intégration DeepSeek
                const hasDeepSeekIntegration = thermalData.neural_system?.deepseek_r1_authentic_integration?.active;
                
                const isIntegrated = hasDeepSeekZone && hasQIBoost && hasDeepSeekIntegration;
                
                this.testResults.push({
                    test: 'Intégration mémoire thermique',
                    correct: isIntegrated,
                    duration: 0.1,
                    details: {
                        hasDeepSeekZone,
                        currentQI,
                        hasQIBoost,
                        hasDeepSeekIntegration
                    }
                });
                
                console.log(`${isIntegrated ? '✅' : '❌'} Zone DeepSeek: ${hasDeepSeekZone ? 'TROUVÉE' : 'MANQUANTE'}`);
                console.log(`${hasQIBoost ? '✅' : '❌'} QI système: ${currentQI} ${hasQIBoost ? '(AUGMENTÉ)' : '(PAS D\'AUGMENTATION)'}`);
                console.log(`${hasDeepSeekIntegration ? '✅' : '❌'} Intégration active: ${hasDeepSeekIntegration ? 'OUI' : 'NON'}`);
                console.log(`${isIntegrated ? '✅' : '❌'} Résultat: ${isIntegrated ? 'INTÉGRATION RÉUSSIE' : 'PROBLÈME D\'INTÉGRATION'}`);
                
            } else {
                console.log('❌ Fichier mémoire thermique non trouvé');
                this.testResults.push({
                    test: 'Intégration mémoire thermique',
                    correct: false,
                    duration: 0.1,
                    error: 'Thermal memory file not found'
                });
            }
        } catch (error) {
            console.log(`❌ Erreur lecture mémoire thermique: ${error.message}`);
            this.testResults.push({
                test: 'Intégration mémoire thermique',
                correct: false,
                duration: 0.1,
                error: error.message
            });
        }
    }

    async queryDeepSeek(question) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', question]);
            
            let output = '';
            let errorOutput = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            ollama.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            // Timeout après 45 secondes
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, 45000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                if (code === 0 && output.trim()) {
                    // Nettoyer la sortie
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    console.log(`⚠️ Erreur Ollama (code ${code}): ${errorOutput}`);
                    resolve(null);
                }
            });
        });
    }

    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(test => test.correct).length;
        const successRate = (passedTests / totalTests) * 100;
        const totalDuration = this.testResults.reduce((sum, test) => sum + test.duration, 0);
        const isAuthentic = successRate >= 60; // Au moins 60% de réussite pour être considéré comme authentique
        
        const report = {
            timestamp: Date.now(),
            total_tests: totalTests,
            passed_tests: passedTests,
            failed_tests: totalTests - passedTests,
            success_rate: successRate,
            total_duration: totalDuration,
            is_authentic: isAuthentic,
            test_details: this.testResults
        };
        
        // Sauvegarder le rapport
        const reportPath = `deepseek_authentic_test_report_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📋 Rapport détaillé sauvegardé: ${reportPath}`);
        
        return report;
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🧪 TEST AUTHENTIQUE DEEPSEEK R1 8B');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Test réel - Pas de simulation - Vérification authentique');
    
    const tester = new AuthenticDeepSeekTester();
    await tester.runAuthenticTests();
}

if (require.main === module) {
    main();
}

module.exports = AuthenticDeepSeekTester;
