#!/usr/bin/env python3
"""
🚀 CLIENT DIRECT DEEPSEEK R1 8B
Communication native sans Ollama
"""

import requests
import json
import sys

class DeepSeekDirectClient:
    def __init__(self):
        self.base_url = "http://localhost:11434"
        
    def query(self, prompt):
        """Envoie une requête au serveur natif"""
        try:
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": "deepseek-r1:8b-native",
                    "prompt": prompt,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('response', 'Pas de réponse')
            else:
                return f"Erreur {response.status_code}: {response.text}"
                
        except Exception as e:
            return f"Erreur connexion: {e}"
    
    def interactive(self):
        """Mode interactif"""
        print("🚀 CLIENT DIRECT DEEPSEEK R1 8B")
        print("================================")
        print("Tapez 'exit' pour quitter")
        print()
        
        while True:
            try:
                prompt = input("DeepSeek> ")
                if prompt.lower() in ['exit', 'quit']:
                    break
                
                if prompt.strip():
                    response = self.query(prompt)
                    print(f"\n{response}\n")
                    
            except KeyboardInterrupt:
                break
        
        print("👋 Au revoir !")

if __name__ == "__main__":
    client = DeepSeekDirectClient()
    
    if len(sys.argv) > 1:
        # Mode commande
        prompt = " ".join(sys.argv[1:])
        response = client.query(prompt)
        print(response)
    else:
        # Mode interactif
        client.interactive()
