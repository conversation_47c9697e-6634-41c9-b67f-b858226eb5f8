#!/usr/bin/env node

/**
 * 🧠 INTERFACE CONSULTATION MÉMOIRE DEEPSEEK R1 8B
 * Accès intelligent à la mémoire thermique
 */

const fs = require('fs');
const readline = require('readline');

class DeepSeekMemoryInterface {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.thermalData = null;
        this.loadMemory();
    }

    loadMemory() {
        try {
            this.thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            console.log('🧠 Mémoire thermique chargée');
        } catch (error) {
            console.error('❌ Erreur chargement mémoire:', error.message);
        }
    }

    async start() {
        console.log('🧠 INTERFACE CONSULTATION MÉMOIRE DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('📚 Consultation intelligente mémoire thermique');
        console.log('🔍 Commandes disponibles:');
        console.log('  /qi - Niveau QI actuel');
        console.log('  /zones - Liste zones thermiques');
        console.log('  /hot - Zones les plus chaudes');
        console.log('  /recent - Informations récentes');
        console.log('  /search <mot> - Recherche par mot-clé');
        console.log('  /status - Statut optimisations');
        console.log('  /exit - Quitter');
        console.log('');
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.promptUser(rl);
    }
    
    promptUser(rl) {
        rl.question('🧠 Mémoire > ', (input) => {
            if (input.trim() === '/exit') {
                console.log('👋 Interface fermée');
                rl.close();
                return;
            }
            
            this.processCommand(input.trim());
            this.promptUser(rl);
        });
    }
    
    processCommand(command) {
        console.log('');
        
        if (command === '/qi') {
            this.showQILevel();
        } else if (command === '/zones') {
            this.showThermalZones();
        } else if (command === '/hot') {
            this.showHotZones();
        } else if (command === '/recent') {
            this.showRecentInfo();
        } else if (command.startsWith('/search ')) {
            const keyword = command.substring(8);
            this.searchMemory(keyword);
        } else if (command === '/status') {
            this.showOptimizationStatus();
        } else {
            console.log('❓ Commande inconnue. Tapez /exit pour quitter.');
        }
        
        console.log('');
    }
    
    showQILevel() {
        const qiLevel = this.thermalData.neural_system?.qi_level || 'Non défini';
        console.log('🧠 NIVEAU QI ACTUEL');
        console.log('==================');
        console.log(`QI: ${qiLevel}`);
        
        if (this.thermalData.neural_system?.total_neurons) {
            console.log(`Neurones: ${this.thermalData.neural_system.total_neurons.toLocaleString()}`);
        }
    }
    
    showThermalZones() {
        console.log('🌡️ ZONES THERMIQUES');
        console.log('==================');
        
        if (this.thermalData.thermal_zones) {
            Object.entries(this.thermalData.thermal_zones).forEach(([name, zone]) => {
                const temp = zone.temperature || 0;
                const entries = zone.entries ? zone.entries.length : 0;
                console.log(`${name}: ${temp}°C (${entries} entrées)`);
            });
        }
    }
    
    showHotZones() {
        console.log('🔥 ZONES LES PLUS CHAUDES');
        console.log('========================');
        
        if (this.thermalData.thermal_zones) {
            const zones = Object.entries(this.thermalData.thermal_zones)
                .map(([name, zone]) => ({
                    name,
                    temperature: zone.temperature || 0,
                    description: zone.description || 'Aucune description'
                }))
                .sort((a, b) => b.temperature - a.temperature)
                .slice(0, 5);
                
            zones.forEach(zone => {
                console.log(`${zone.name}: ${zone.temperature}°C`);
                console.log(`  → ${zone.description}`);
            });
        }
    }
    
    showRecentInfo() {
        console.log('📅 INFORMATIONS RÉCENTES');
        console.log('=======================');
        
        const allEntries = [];
        
        if (this.thermalData.thermal_zones) {
            Object.entries(this.thermalData.thermal_zones).forEach(([zoneName, zone]) => {
                if (zone.entries) {
                    zone.entries.forEach(entry => {
                        allEntries.push({
                            ...entry,
                            zone: zoneName
                        });
                    });
                }
            });
        }
        
        const recentEntries = allEntries
            .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
            .slice(0, 5);
            
        recentEntries.forEach(entry => {
            const date = new Date((entry.timestamp || 0) * 1000).toLocaleString('fr-FR');
            console.log(`[${date}] ${entry.zone}`);
            console.log(`  → ${entry.content?.substring(0, 100)}...`);
        });
    }
    
    searchMemory(keyword) {
        console.log(`🔍 RECHERCHE: "${keyword}"`);
        console.log('========================');
        
        const results = [];
        
        if (this.thermalData.thermal_zones) {
            Object.entries(this.thermalData.thermal_zones).forEach(([zoneName, zone]) => {
                if (zone.entries) {
                    zone.entries.forEach(entry => {
                        if (entry.content?.toLowerCase().includes(keyword.toLowerCase())) {
                            results.push({
                                zone: zoneName,
                                content: entry.content,
                                temperature: zone.temperature
                            });
                        }
                    });
                }
            });
        }
        
        if (results.length > 0) {
            results.forEach(result => {
                console.log(`Zone: ${result.zone} (${result.temperature}°C)`);
                console.log(`  → ${result.content.substring(0, 150)}...`);
                console.log('');
            });
        } else {
            console.log('Aucun résultat trouvé.');
        }
    }
    
    showOptimizationStatus() {
        console.log('⚡ STATUT OPTIMISATIONS');
        console.log('=====================');
        
        const optimizations = [
            { key: 'cognitive_system', name: 'Système Cognitif' },
            { key: 'true_kyber_accelerators', name: 'Accélérateurs Kyber' },
            { key: 'vitamin_system', name: 'Vitamines Performance' },
            { key: 'french_language_system', name: 'Français Par Défaut' },
            { key: 'direct_engine', name: 'Moteur Direct' },
            { key: 'layer_cleaning_system', name: 'Nettoyage Couches' }
        ];
        
        optimizations.forEach(opt => {
            const status = this.thermalData[opt.key] ? '✅ ACTIF' : '❌ INACTIF';
            console.log(`${opt.name}: ${status}`);
        });
    }
}

const memoryInterface = new DeepSeekMemoryInterface();
memoryInterface.start();
