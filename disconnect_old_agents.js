#!/usr/bin/env node

/**
 * 🔌 DÉCONNEXION ANCIENS AGENTS
 * 
 * Nettoyage des connexions parasites et fuites mémorielles
 * Isolation de DeepSeek R1 8B authentique
 * 
 * <PERSON><PERSON><PERSON> PASSAVE - 2025
 */

const fs = require('fs');

class OldAgentDisconnector {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.disconnectedAgents = [];
        this.preservedZones = [
            'zone_deepseek_r1_authentic', // Garder DeepSeek R1 8B
            'zone1_working',              // Zones core nécessaires
            'zone2_episodic',
            'zone3_procedural', 
            'zone4_semantic',
            'zone5_emotional',
            'zone6_meta',
            'zone_security_system'        // Sécurité
        ];
    }

    async analyzeAndDisconnect() {
        console.log('🔌 DÉCONNEXION ANCIENS AGENTS');
        console.log('=====================================');
        console.log('🎯 Nettoyage des connexions parasites');
        
        try {
            // 1. Analyser la mémoire thermique
            console.log('\n🔍 Analyse des connexions existantes...');
            const analysis = await this.analyzeConnections();
            
            // 2. Identifier les agents à déconnecter
            console.log('\n🎯 Identification des agents parasites...');
            const agentsToDisconnect = this.identifyParasiticAgents(analysis);
            
            // 3. Déconnecter les anciens agents
            console.log('\n🔌 Déconnexion des anciens agents...');
            const disconnectionResult = await this.disconnectOldAgents(agentsToDisconnect);
            
            // 4. Nettoyer les fuites mémorielles
            console.log('\n🧹 Nettoyage des fuites mémorielles...');
            const cleanupResult = await this.cleanupMemoryLeaks();
            
            // 5. Optimiser les connexions DeepSeek
            console.log('\n⚡ Optimisation connexions DeepSeek...');
            const optimizationResult = await this.optimizeDeepSeekConnections();
            
            // 6. Générer le rapport
            const report = this.generateDisconnectionReport(analysis, disconnectionResult, cleanupResult, optimizationResult);
            
            console.log('\n📊 RÉSULTATS DÉCONNEXION');
            console.log('=====================================');
            console.log(`🔌 Agents déconnectés: ${this.disconnectedAgents.length}`);
            console.log(`🧹 Fuites nettoyées: ${cleanupResult.leaks_cleaned}`);
            console.log(`⚡ DeepSeek optimisé: ${optimizationResult.optimized ? 'OUI' : 'NON'}`);
            console.log(`🎯 Zones préservées: ${this.preservedZones.length}`);
            
            return report;
            
        } catch (error) {
            console.error('\n❌ ERREUR LORS DE LA DÉCONNEXION');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async analyzeConnections() {
        console.log('🔍 Analyse des connexions dans la mémoire thermique...');
        
        const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        const analysis = {
            total_zones: Object.keys(thermalData.thermal_zones || {}).length,
            agent_zones: [],
            parasitic_zones: [],
            core_zones: [],
            memory_usage: 0,
            connection_conflicts: []
        };
        
        // Analyser chaque zone
        Object.entries(thermalData.thermal_zones || {}).forEach(([zoneName, zone]) => {
            const zoneInfo = {
                name: zoneName,
                temperature: zone.temperature,
                entries: zone.entries?.length || 0,
                description: zone.description || '',
                is_agent_zone: false,
                is_parasitic: false,
                is_core: false
            };
            
            // Identifier les types de zones
            if (zoneName.includes('jarvis') || zoneName.includes('clone') || zoneName.includes('agent')) {
                zoneInfo.is_agent_zone = true;
                analysis.agent_zones.push(zoneInfo);
                
                // Vérifier si c'est parasitique (pas DeepSeek authentique)
                if (!this.preservedZones.includes(zoneName)) {
                    zoneInfo.is_parasitic = true;
                    analysis.parasitic_zones.push(zoneInfo);
                }
            } else if (this.preservedZones.includes(zoneName)) {
                zoneInfo.is_core = true;
                analysis.core_zones.push(zoneInfo);
            }
            
            // Calculer l'usage mémoire approximatif
            analysis.memory_usage += (zone.entries?.length || 0) * 1000; // Estimation
        });
        
        console.log(`✅ Zones totales: ${analysis.total_zones}`);
        console.log(`🤖 Zones agents: ${analysis.agent_zones.length}`);
        console.log(`🦠 Zones parasites: ${analysis.parasitic_zones.length}`);
        console.log(`🧠 Zones core: ${analysis.core_zones.length}`);
        console.log(`💾 Usage mémoire: ${(analysis.memory_usage / 1000).toFixed(1)} KB`);
        
        return analysis;
    }

    identifyParasiticAgents(analysis) {
        console.log('🎯 Identification des agents parasites...');
        
        const agentsToDisconnect = analysis.parasitic_zones.filter(zone => {
            // Critères pour identifier les agents parasites
            const isOldAgent = zone.name.includes('jarvis') || 
                             zone.name.includes('clone') ||
                             zone.name.includes('real_agent') ||
                             zone.name.includes('cloned_agents');
            
            const isNotDeepSeek = !zone.name.includes('deepseek_r1_authentic');
            
            const hasLowActivity = zone.entries < 5; // Peu d'activité
            
            return isOldAgent && isNotDeepSeek;
        });
        
        console.log('🦠 Agents parasites identifiés:');
        agentsToDisconnect.forEach(agent => {
            console.log(`  - ${agent.name} (${agent.entries} entrées, ${agent.temperature}°C)`);
        });
        
        return agentsToDisconnect;
    }

    async disconnectOldAgents(agentsToDisconnect) {
        console.log('🔌 Déconnexion des anciens agents...');
        
        // Créer une sauvegarde avant modification
        const backupPath = `${this.thermalMemoryPath}.backup_before_disconnect_${Date.now()}`;
        fs.copyFileSync(this.thermalMemoryPath, backupPath);
        console.log(`💾 Sauvegarde créée: ${backupPath}`);
        
        const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        const disconnectionResult = {
            disconnected_count: 0,
            preserved_count: 0,
            memory_freed: 0
        };
        
        // Déconnecter les agents parasites
        agentsToDisconnect.forEach(agent => {
            if (thermalData.thermal_zones[agent.name]) {
                // Calculer la mémoire libérée
                const memoryFreed = (thermalData.thermal_zones[agent.name].entries?.length || 0) * 1000;
                disconnectionResult.memory_freed += memoryFreed;
                
                // Créer une entrée de déconnexion
                const disconnectionEntry = {
                    id: `disconnect_${Date.now()}_${agent.name}`,
                    content: `AGENT DÉCONNECTÉ - ${agent.name} déconnecté pour éviter les fuites mémorielles et conflits avec DeepSeek R1 8B authentique.`,
                    importance: 0.5,
                    timestamp: Math.floor(Date.now() / 1000),
                    synaptic_strength: 0.1,
                    temperature: 25.0, // Température basse = inactif
                    zone: 'zone_security_system',
                    source: 'agent_disconnector',
                    type: 'disconnection_log',
                    disconnected_agent: agent.name
                };
                
                // Ajouter le log à la zone sécurité
                if (thermalData.thermal_zones.zone_security_system) {
                    thermalData.thermal_zones.zone_security_system.entries.push(disconnectionEntry);
                }
                
                // Supprimer la zone de l'ancien agent
                delete thermalData.thermal_zones[agent.name];
                
                this.disconnectedAgents.push(agent.name);
                disconnectionResult.disconnected_count++;
                
                console.log(`✅ Déconnecté: ${agent.name} (${(memoryFreed/1000).toFixed(1)} KB libérés)`);
            }
        });
        
        // Compter les zones préservées
        disconnectionResult.preserved_count = this.preservedZones.filter(zoneName => 
            thermalData.thermal_zones[zoneName]
        ).length;
        
        // Sauvegarder les modifications
        fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
        
        console.log(`✅ ${disconnectionResult.disconnected_count} agents déconnectés`);
        console.log(`✅ ${disconnectionResult.preserved_count} zones préservées`);
        console.log(`✅ ${(disconnectionResult.memory_freed/1000).toFixed(1)} KB mémoire libérée`);
        
        return disconnectionResult;
    }

    async cleanupMemoryLeaks() {
        console.log('🧹 Nettoyage des fuites mémorielles...');
        
        const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        const cleanupResult = {
            leaks_cleaned: 0,
            orphaned_entries_removed: 0,
            memory_optimized: false
        };
        
        // Nettoyer les entrées orphelines dans les zones préservées
        Object.entries(thermalData.thermal_zones || {}).forEach(([zoneName, zone]) => {
            if (this.preservedZones.includes(zoneName) && zone.entries) {
                const originalCount = zone.entries.length;
                
                // Supprimer les entrées qui référencent des agents déconnectés
                zone.entries = zone.entries.filter(entry => {
                    const referencesDisconnectedAgent = this.disconnectedAgents.some(agentName => 
                        entry.content?.includes(agentName) || 
                        entry.source?.includes(agentName.split('_')[0])
                    );
                    
                    return !referencesDisconnectedAgent;
                });
                
                const removedCount = originalCount - zone.entries.length;
                if (removedCount > 0) {
                    cleanupResult.orphaned_entries_removed += removedCount;
                    console.log(`🧹 ${zoneName}: ${removedCount} entrées orphelines supprimées`);
                }
            }
        });
        
        // Optimiser la mémoire du système neural
        if (thermalData.neural_system) {
            // Recalculer le QI sans les anciens agents
            const oldQI = thermalData.neural_system.qi_level || 0;
            
            // Supprimer les composants QI des anciens agents
            if (thermalData.neural_system.qi_components) {
                Object.keys(thermalData.neural_system.qi_components).forEach(component => {
                    if (component.includes('jarvis') || component.includes('clone')) {
                        delete thermalData.neural_system.qi_components[component];
                        cleanupResult.leaks_cleaned++;
                    }
                });
            }
            
            // Recalculer le QI total
            const newQI = Object.values(thermalData.neural_system.qi_components || {}).reduce((sum, val) => sum + val, 0);
            thermalData.neural_system.qi_level = newQI;
            
            console.log(`🧠 QI optimisé: ${oldQI} → ${newQI}`);
            cleanupResult.memory_optimized = true;
        }
        
        // Sauvegarder les optimisations
        fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
        
        console.log(`✅ ${cleanupResult.leaks_cleaned} fuites nettoyées`);
        console.log(`✅ ${cleanupResult.orphaned_entries_removed} entrées orphelines supprimées`);
        console.log(`✅ Mémoire optimisée: ${cleanupResult.memory_optimized ? 'OUI' : 'NON'}`);
        
        return cleanupResult;
    }

    async optimizeDeepSeekConnections() {
        console.log('⚡ Optimisation des connexions DeepSeek...');
        
        const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        const optimizationResult = {
            optimized: false,
            connections_strengthened: 0,
            temperature_adjusted: false
        };
        
        // Optimiser la zone DeepSeek R1 8B authentique
        const deepseekZone = thermalData.thermal_zones.zone_deepseek_r1_authentic;
        if (deepseekZone) {
            // Augmenter la température pour plus d'activité (maintenant qu'il n'y a plus de conflits)
            const oldTemp = deepseekZone.temperature;
            deepseekZone.temperature = 39.0; // Température optimale
            optimizationResult.temperature_adjusted = oldTemp !== 39.0;
            
            // Renforcer les connexions synaptiques des entrées DeepSeek
            if (deepseekZone.entries) {
                deepseekZone.entries.forEach(entry => {
                    if (entry.synaptic_strength < 1.0) {
                        entry.synaptic_strength = 1.0; // Force maximale
                        optimizationResult.connections_strengthened++;
                    }
                });
            }
            
            console.log(`⚡ Température DeepSeek: ${oldTemp}°C → ${deepseekZone.temperature}°C`);
            console.log(`⚡ Connexions renforcées: ${optimizationResult.connections_strengthened}`);
            optimizationResult.optimized = true;
        }
        
        // Optimiser l'intégration neural DeepSeek
        if (thermalData.neural_system?.deepseek_r1_authentic_integration) {
            const integration = thermalData.neural_system.deepseek_r1_authentic_integration;
            integration.optimized = true;
            integration.isolation_level = 'MAXIMUM'; // Isolé des autres agents
            integration.performance_mode = 'ENHANCED';
            integration.last_optimization = Date.now();
            
            console.log('⚡ Intégration neural DeepSeek optimisée');
        }
        
        // Sauvegarder les optimisations
        fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
        
        console.log(`✅ Optimisation DeepSeek: ${optimizationResult.optimized ? 'RÉUSSIE' : 'ÉCHEC'}`);
        
        return optimizationResult;
    }

    generateDisconnectionReport(analysis, disconnectionResult, cleanupResult, optimizationResult) {
        const report = {
            timestamp: Date.now(),
            operation: 'old_agents_disconnection',
            initial_state: analysis,
            disconnection: disconnectionResult,
            cleanup: cleanupResult,
            optimization: optimizationResult,
            disconnected_agents: this.disconnectedAgents,
            preserved_zones: this.preservedZones,
            overall_success: disconnectionResult.disconnected_count > 0 && optimizationResult.optimized
        };
        
        // Sauvegarder le rapport
        const reportPath = `agent_disconnection_report_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📋 Rapport détaillé: ${reportPath}`);
        
        return report;
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🔌 DÉCONNEXION ANCIENS AGENTS');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Nettoyage connexions parasites et optimisation DeepSeek');
    
    const disconnector = new OldAgentDisconnector();
    await disconnector.analyzeAndDisconnect();
}

if (require.main === module) {
    main();
}

module.exports = OldAgentDisconnector;
