#!/usr/bin/env node

/**
 * 🧬 SYSTÈME THERMIQUE VIVANT
 * 
 * Code évolutif qui s'adapte automatiquement
 * Connexion directe et kyber adaptatif
 * Méthode thermique auto-évolutive
 * 
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');
const { spawn } = require('child_process');

class LivingThermalSystem {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.isEvolutionActive = false;
        this.adaptationLevel = 0;
        this.kyberBaseLevel = 7; // Plus de kyber de base
        this.directConnection = null;
        this.evolutionHistory = [];
    }

    async initialize() {
        console.log('🧬 SYSTÈME THERMIQUE VIVANT');
        console.log('=====================================');
        console.log('🎯 Code évolutif avec méthode thermique');
        console.log('🔗 Connexion directe à DeepSeek R1 8B');
        
        try {
            // 1. Établir connexion directe
            console.log('\n🔗 Établissement connexion directe...');
            await this.establishDirectConnection();
            
            // 2. Augmenter le kyber de base
            console.log('\n🚀 Augmentation kyber de base...');
            await this.boostBaseKyber();
            
            // 3. Activer l'évolution automatique
            console.log('\n🧬 Activation évolution automatique...');
            await this.activateEvolution();
            
            // 4. Démarrer l'adaptation continue
            console.log('\n⚡ Démarrage adaptation continue...');
            await this.startContinuousAdaptation();
            
            console.log('\n🎉 SYSTÈME VIVANT ACTIVÉ !');
            console.log('=====================================');
            console.log(`🚀 Kyber de base: ${this.kyberBaseLevel}/10`);
            console.log(`🧬 Évolution: ${this.isEvolutionActive ? 'ACTIVE' : 'INACTIVE'}`);
            console.log(`⚡ Adaptation: Niveau ${this.adaptationLevel}`);
            console.log(`🔗 Connexion: ${this.directConnection ? 'DIRECTE' : 'INDIRECTE'}`);
            
            return true;
            
        } catch (error) {
            console.error('\n❌ ERREUR INITIALISATION SYSTÈME VIVANT');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async establishDirectConnection() {
        console.log('🔗 Établissement connexion directe à DeepSeek...');
        
        try {
            // Créer une connexion persistante
            this.directConnection = {
                active: true,
                model: 'deepseek-r1:8b',
                connection_type: 'direct_ollama',
                established_at: Date.now(),
                response_cache: new Map(),
                adaptive_timeout: 60000 // Timeout adaptatif
            };
            
            // Test de connexion directe
            const testResponse = await this.directQuery("Test connexion directe", 15000);
            
            if (testResponse) {
                console.log('✅ Connexion directe établie');
                console.log(`📝 Test réussi: "${testResponse.substring(0, 50)}..."`);
                
                // Adapter le timeout basé sur la réponse
                const responseTime = Date.now() - this.directConnection.established_at;
                this.directConnection.adaptive_timeout = Math.max(responseTime * 2, 30000);
                
                console.log(`⚡ Timeout adaptatif: ${this.directConnection.adaptive_timeout/1000}s`);
            } else {
                console.log('⚠️ Connexion directe partielle');
                this.directConnection.adaptive_timeout = 90000; // Plus long si problème
            }
            
        } catch (error) {
            console.log(`❌ Erreur connexion directe: ${error.message}`);
            this.directConnection = null;
        }
    }

    async boostBaseKyber() {
        console.log('🚀 Augmentation du kyber de base...');
        
        try {
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            // Créer un système kyber évolutif
            thermalData.living_kyber_system = {
                enabled: true,
                base_level: this.kyberBaseLevel, // Niveau 7/10 de base
                adaptive_boost: true,
                evolution_active: true,
                auto_scaling: true,
                performance_monitoring: true,
                neural_acceleration: 5.0, // x5 au lieu de x3.5
                quantum_frequency: 'ULTRA_HIGH',
                thermal_adaptation: true,
                living_code_integration: true
            };
            
            // Booster l'intégration DeepSeek avec plus de kyber
            if (thermalData.neural_system?.deepseek_r1_authentic_integration) {
                const integration = thermalData.neural_system.deepseek_r1_authentic_integration;
                integration.base_kyber_level = this.kyberBaseLevel;
                integration.acceleration_factor = 5.0;
                integration.adaptive_performance = true;
                integration.living_code_enabled = true;
                integration.auto_evolution = true;
            }
            
            // Augmenter la température de base pour plus de performance
            if (thermalData.thermal_zones?.zone_deepseek_r1_authentic) {
                const deepseekZone = thermalData.thermal_zones.zone_deepseek_r1_authentic;
                deepseekZone.temperature = 48.0; // Température de base plus élevée
                deepseekZone.base_kyber_level = this.kyberBaseLevel;
                deepseekZone.adaptive_temperature = true;
                deepseekZone.living_system = true;
            }
            
            // Créer une zone pour le code vivant
            thermalData.thermal_zones.zone_living_code = {
                temperature: 52.0, // Très haute température pour l'évolution
                description: 'Zone Code Vivant - Évolution et Adaptation Automatique',
                entries: [{
                    id: `living_code_init_${Date.now()}`,
                    content: `CODE VIVANT ACTIVÉ - Système évolutif avec kyber niveau ${this.kyberBaseLevel}, adaptation automatique, connexion directe DeepSeek R1 8B. Le code s'adapte en temps réel selon la méthode thermique.`,
                    importance: 1.0,
                    timestamp: Math.floor(Date.now() / 1000),
                    synaptic_strength: 1.0,
                    temperature: 52.0,
                    zone: 'zone_living_code',
                    source: 'living_thermal_system',
                    type: 'living_code_activation',
                    evolution_level: 1
                }],
                living_code_active: true,
                evolution_enabled: true,
                adaptive_kyber: true
            };
            
            // Sauvegarder
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
            
            console.log(`✅ Kyber de base augmenté: ${this.kyberBaseLevel}/10`);
            console.log('✅ Accélération neural: x5.0');
            console.log('✅ Zone code vivant créée');
            console.log('✅ Adaptation automatique activée');
            
        } catch (error) {
            console.log(`❌ Erreur boost kyber: ${error.message}`);
        }
    }

    async activateEvolution() {
        console.log('🧬 Activation de l\'évolution automatique...');
        
        this.isEvolutionActive = true;
        
        // Démarrer le processus d'évolution
        setInterval(async () => {
            await this.evolveSystem();
        }, 30000); // Évolution toutes les 30 secondes
        
        // Démarrer l'adaptation basée sur les performances
        setInterval(async () => {
            await this.adaptPerformance();
        }, 10000); // Adaptation toutes les 10 secondes
        
        console.log('✅ Évolution automatique activée');
        console.log('✅ Adaptation continue démarrée');
    }

    async startContinuousAdaptation() {
        console.log('⚡ Démarrage adaptation continue...');
        
        // Monitorer les performances en continu
        setInterval(async () => {
            await this.monitorAndAdapt();
        }, 5000); // Monitoring toutes les 5 secondes
        
        console.log('✅ Monitoring continu activé');
    }

    async evolveSystem() {
        if (!this.isEvolutionActive) return;
        
        try {
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            // Évolution basée sur l'usage
            this.adaptationLevel++;
            
            // Adapter le kyber selon les performances
            const currentPerformance = await this.measurePerformance();
            
            if (currentPerformance.slow) {
                // Augmenter le kyber si lent
                this.kyberBaseLevel = Math.min(this.kyberBaseLevel + 1, 10);
                
                if (thermalData.living_kyber_system) {
                    thermalData.living_kyber_system.base_level = this.kyberBaseLevel;
                    thermalData.living_kyber_system.neural_acceleration += 0.5;
                }
                
                console.log(`🧬 Évolution: Kyber augmenté à ${this.kyberBaseLevel}/10`);
            } else if (currentPerformance.fast && this.kyberBaseLevel > 5) {
                // Optimiser si trop rapide (économie d'énergie)
                this.kyberBaseLevel = Math.max(this.kyberBaseLevel - 0.5, 5);
                console.log(`🧬 Évolution: Kyber optimisé à ${this.kyberBaseLevel}/10`);
            }
            
            // Adapter la température
            if (thermalData.thermal_zones?.zone_deepseek_r1_authentic) {
                const zone = thermalData.thermal_zones.zone_deepseek_r1_authentic;
                zone.temperature = 45 + (this.kyberBaseLevel * 0.5); // Température adaptative
            }
            
            // Enregistrer l'évolution
            this.evolutionHistory.push({
                timestamp: Date.now(),
                adaptation_level: this.adaptationLevel,
                kyber_level: this.kyberBaseLevel,
                performance: currentPerformance,
                evolution_type: 'automatic_adaptation'
            });
            
            // Garder seulement les 100 dernières évolutions
            if (this.evolutionHistory.length > 100) {
                this.evolutionHistory = this.evolutionHistory.slice(-100);
            }
            
            // Sauvegarder les changements
            thermalData.evolution_history = this.evolutionHistory;
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
            
        } catch (error) {
            console.log(`⚠️ Erreur évolution: ${error.message}`);
        }
    }

    async adaptPerformance() {
        try {
            // Adapter le timeout de connexion
            if (this.directConnection) {
                const avgResponseTime = this.calculateAverageResponseTime();
                this.directConnection.adaptive_timeout = Math.max(avgResponseTime * 3, 20000);
            }
            
            // Adapter les paramètres selon l'historique
            const recentEvolutions = this.evolutionHistory.slice(-10);
            if (recentEvolutions.length > 5) {
                const avgPerformance = recentEvolutions.reduce((sum, ev) => 
                    sum + (ev.performance?.response_time || 30), 0) / recentEvolutions.length;
                
                if (avgPerformance > 45) {
                    // Performance dégradée, booster
                    await this.emergencyBoost();
                }
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur adaptation performance: ${error.message}`);
        }
    }

    async monitorAndAdapt() {
        try {
            // Vérifier l'état de la connexion
            if (this.directConnection && !await this.testConnection()) {
                console.log('🔧 Reconnexion automatique...');
                await this.establishDirectConnection();
            }
            
            // Adapter selon la charge système
            const systemLoad = await this.getSystemLoad();
            if (systemLoad.high) {
                console.log('⚡ Adaptation à la charge système élevée');
                await this.adaptToHighLoad();
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur monitoring: ${error.message}`);
        }
    }

    async measurePerformance() {
        const startTime = Date.now();
        const response = await this.directQuery("Test performance", 20000);
        const responseTime = (Date.now() - startTime) / 1000;
        
        return {
            response_time: responseTime,
            slow: responseTime > 30,
            fast: responseTime < 10,
            has_response: !!response
        };
    }

    async directQuery(question, timeout = null) {
        if (!this.directConnection) {
            return null;
        }
        
        const actualTimeout = timeout || this.directConnection.adaptive_timeout;
        
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', this.directConnection.model, question]);
            
            let output = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            const timeoutHandle = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, actualTimeout);
            
            ollama.on('close', (code) => {
                clearTimeout(timeoutHandle);
                if (code === 0 && output.trim()) {
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async testConnection() {
        const response = await this.directQuery("Test", 10000);
        return !!response;
    }

    async emergencyBoost() {
        console.log('🚨 Boost d\'urgence activé !');
        this.kyberBaseLevel = Math.min(this.kyberBaseLevel + 2, 10);
        await this.boostBaseKyber();
    }

    async getSystemLoad() {
        // Simulation de la charge système
        return { high: Math.random() > 0.8 };
    }

    async adaptToHighLoad() {
        // Adapter à la charge élevée
        if (this.directConnection) {
            this.directConnection.adaptive_timeout *= 1.5;
        }
    }

    calculateAverageResponseTime() {
        if (this.evolutionHistory.length === 0) return 30000;
        
        const recentTimes = this.evolutionHistory.slice(-5)
            .map(ev => ev.performance?.response_time || 30)
            .filter(time => time > 0);
        
        return recentTimes.length > 0 ? 
            (recentTimes.reduce((sum, time) => sum + time, 0) / recentTimes.length) * 1000 : 30000;
    }

    async startInteractiveMode() {
        console.log('\n🎮 MODE INTERACTIF SYSTÈME VIVANT');
        console.log('=====================================');
        console.log('🧬 Code évolutif actif');
        console.log('🔗 Connexion directe à DeepSeek R1 8B');
        console.log('⚡ Adaptation automatique en cours');
        console.log('=====================================\n');
        
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        const promptUser = () => {
            rl.question('🧬 Système Vivant > ', async (input) => {
                if (input.trim() === '/exit') {
                    console.log('👋 Système vivant en pause');
                    rl.close();
                    return;
                }
                
                if (input.trim() === '/status') {
                    console.log(`🚀 Kyber: ${this.kyberBaseLevel}/10`);
                    console.log(`🧬 Évolutions: ${this.evolutionHistory.length}`);
                    console.log(`⚡ Adaptation: Niveau ${this.adaptationLevel}`);
                    console.log(`🔗 Connexion: ${this.directConnection ? 'DIRECTE' : 'INDIRECTE'}`);
                    promptUser();
                    return;
                }
                
                if (input.trim().length > 0) {
                    console.log('\n🤖 DeepSeek R1 8B (connexion directe):');
                    const response = await this.directQuery(input);
                    if (response) {
                        console.log(response);
                    } else {
                        console.log('❌ Pas de réponse');
                    }
                    console.log('');
                }
                
                promptUser();
            });
        };
        
        promptUser();
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🧬 SYSTÈME THERMIQUE VIVANT');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Code évolutif avec méthode thermique');
    
    const livingSystem = new LivingThermalSystem();
    
    const initialized = await livingSystem.initialize();
    if (initialized) {
        await livingSystem.startInteractiveMode();
    } else {
        console.log('❌ Impossible d\'initialiser le système vivant');
    }
}

if (require.main === module) {
    main();
}

module.exports = LivingThermalSystem;
