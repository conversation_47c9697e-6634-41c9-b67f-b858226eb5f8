{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:4e4c34e8cd10d743df92c23d3ef64393f4d57d4d064c09a82f51565d75c93750", "size": 561}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:e6a7edc1a4d7d9b2de136a221a57336b76316cfe53a252aeba814496c5ae439d", "size": **********, "from": "deepseek-r1:8b"}, {"mediaType": "application/vnd.ollama.image.template", "digest": "sha256:c5ad996bda6eed4df6e3b605a9869647624851ac248209d22fd5e2c0cc1121d3", "size": 556, "from": "deepseek-r1:8b"}, {"mediaType": "application/vnd.ollama.image.license", "digest": "sha256:6e4c38e1172f42fdbff13edf9a7a017679fb82b0fde415a3e8b3c31c6ed4a4e4", "size": 1065, "from": "deepseek-r1:8b"}, {"mediaType": "application/vnd.ollama.image.system", "digest": "sha256:f48d9f776438312195e4c92304f181f6abd7d39c98a06d94f98793a07b23528e", "size": 3049}, {"mediaType": "application/vnd.ollama.image.params", "digest": "sha256:395dc832632fcaae14b81b020f1bf02a6bba6e581f70037c509acacb39a5f648", "size": 178}]}