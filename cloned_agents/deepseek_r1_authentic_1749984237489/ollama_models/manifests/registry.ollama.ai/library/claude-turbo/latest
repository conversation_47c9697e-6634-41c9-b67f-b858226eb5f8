{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:84d28d747525037d6932fe75ace6e5f7d16d5d120afa49ce0369f0bb3c7cd2a0", "size": 487}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:92da6238854f2fa902d8b2ad79d548536af1d3ab06821f323bd5bbcea2013276", "size": 4083015904, "from": "/Users/<USER>/.ollama/models/blobs/sha256-92da6238854f2fa902d8b2ad79d548536af1d3ab06821f323bd5bbcea2013276"}, {"mediaType": "application/vnd.ollama.image.template", "digest": "sha256:af2b8fe4710f8eaedbd321e98ba3eeb4b6ed38ab5ccbb859d2c37aa9c3e0f5ac", "size": 156}, {"mediaType": "application/vnd.ollama.image.system", "digest": "sha256:44126adbb4c23cf08228fbdd17a0fca1324dee0747c58ab514f33601753eaefa", "size": 1032}, {"mediaType": "application/vnd.ollama.image.params", "digest": "sha256:f2b671675468bcd7e8b3edcfaf8a921c280168a3e097ffe609d4c4efb6ea67e8", "size": 108}]}