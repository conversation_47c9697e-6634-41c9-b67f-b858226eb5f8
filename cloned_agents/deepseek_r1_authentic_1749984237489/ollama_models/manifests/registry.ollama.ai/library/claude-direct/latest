{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:2bf2df878add48a709cb23deb1116623a7e2c9565ad024183b1afd9ca473e716", "size": 487}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:92da6238854f2fa902d8b2ad79d548536af1d3ab06821f323bd5bbcea2013276", "size": 4083015904, "from": "/Users/<USER>/.ollama/models/blobs/sha256-92da6238854f2fa902d8b2ad79d548536af1d3ab06821f323bd5bbcea2013276"}, {"mediaType": "application/vnd.ollama.image.template", "digest": "sha256:af2b8fe4710f8eaedbd321e98ba3eeb4b6ed38ab5ccbb859d2c37aa9c3e0f5ac", "size": 156}, {"mediaType": "application/vnd.ollama.image.system", "digest": "sha256:14cdfa20487bec6d355cf75313492be598b7c3cd05520a8fef1eccf2c585e57f", "size": 1664}, {"mediaType": "application/vnd.ollama.image.params", "digest": "sha256:58ae3c5367a0dedf8eb995c7a5efd756fa3d69726df0178c0aed1477a93f52bb", "size": 75}]}