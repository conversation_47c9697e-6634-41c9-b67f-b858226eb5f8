{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:19902f6a1192135e797b3ac05340bdabc909e67b795bebaa89bdc8920ce6da4a", "size": 487}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:92da6238854f2fa902d8b2ad79d548536af1d3ab06821f323bd5bbcea2013276", "size": 4083015904, "from": "/Users/<USER>/.ollama/models/blobs/sha256-92da6238854f2fa902d8b2ad79d548536af1d3ab06821f323bd5bbcea2013276"}, {"mediaType": "application/vnd.ollama.image.template", "digest": "sha256:af2b8fe4710f8eaedbd321e98ba3eeb4b6ed38ab5ccbb859d2c37aa9c3e0f5ac", "size": 156}, {"mediaType": "application/vnd.ollama.image.system", "digest": "sha256:4bed512762d4f1192a2f013fc1f568274e9befc7e2b666812017c1de2fa3a895", "size": 907}, {"mediaType": "application/vnd.ollama.image.params", "digest": "sha256:962e09fa45b6edf46fb46f024e042f7b6f316b64ddc4cd646594736a34a86a23", "size": 64}]}