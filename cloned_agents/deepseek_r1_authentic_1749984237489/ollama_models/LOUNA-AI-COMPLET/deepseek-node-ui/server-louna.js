/**
 * Serveur Louna - Interface cognitive avancée
 * Version simplifiée pour tester la mémoire thermique
 */

const express = require('express');
const http = require('http');
const path = require('path');
const fs = require('fs');
const socketIo = require('socket.io');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3002;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Servir les fichiers statiques à partir du disque externe
app.use(express.static('/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui/public'));
// Garder aussi l'accès aux fichiers statiques locaux (fallback)
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
// Modifier le chemin pour pointer vers les templates sur le disque externe
app.set('views', '/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui/views');

// Créer les dossiers nécessaires s'ils n'existent pas
const MEMORY_DIR = path.join(__dirname, 'data/memory');
if (!fs.existsSync(MEMORY_DIR)) {
  fs.mkdirSync(MEMORY_DIR, { recursive: true });
}

// Charger les services
const ThermalMemory = require('./thermal-memory/thermal-memory');
const BrainPresence = require('./lib/brain-presence');

// INTÉGRATION DEEPSEEK R1 8B DIRECTE DANS MÉMOIRE THERMIQUE
const { spawn } = require('child_process');

// Initialiser la mémoire thermique RÉELLE
const thermalMemoryPath = '/Volumes/seagate/Louna_Electron_Latest/claude-agent-download/thermal_memory_real_clones_1749979850296.json';
let realThermalMemory = null;

// Charger la vraie mémoire thermique de Jean-Luc
try {
    if (fs.existsSync(thermalMemoryPath)) {
        realThermalMemory = JSON.parse(fs.readFileSync(thermalMemoryPath, 'utf8'));
        console.log('✅ VRAIE mémoire thermique de Jean-Luc chargée');
        console.log(`🧠 QI système: ${realThermalMemory.neural_system?.qi_level || 'Unknown'}`);
        console.log(`🧬 Neurones: ${realThermalMemory.neural_system?.total_neurons?.toLocaleString() || 'Unknown'}`);
    } else {
        console.log('⚠️ Mémoire thermique principale non trouvée, utilisation locale');
    }
} catch (error) {
    console.log('⚠️ Erreur chargement mémoire thermique principale:', error.message);
}

// Initialiser la mémoire thermique locale (fallback)
const thermalMemory = new ThermalMemory(path.join(__dirname, 'data/memory/thermal_memory.json'));
console.log('Mémoire thermique locale initialisée');

// Initialiser le service de présence cérébrale
const brainPresence = new BrainPresence(thermalMemory);
console.log('Service de présence cérébrale initialisé');
console.log('Service de présence cérébrale activé');

// CLASSE DEEPSEEK R1 8B INTÉGRÉE DIRECTEMENT
class DeepSeekR1ThermalIntegration {
    constructor() {
        this.modelName = 'deepseek-r1:8b';
        this.ollamaUrl = 'http://localhost:11434';
        this.realThermalMemory = realThermalMemory;
        this.localThermalMemory = thermalMemory;
        this.agentId = `deepseek_r1_integrated_${Date.now()}`;

        console.log('🤖 DeepSeek R1 8B Thermal Integration initialisée');
        console.log(`🆔 Agent ID: ${this.agentId}`);

        // Intégrer immédiatement dans la mémoire thermique
        this.integrateIntoThermalMemory();
    }

    // Intégrer DeepSeek dans la vraie mémoire thermique
    integrateIntoThermalMemory() {
        if (!this.realThermalMemory) {
            console.log('⚠️ Pas de vraie mémoire thermique, intégration locale seulement');
            return;
        }

        // Créer l'entrée DeepSeek dans la mémoire thermique
        if (!this.realThermalMemory.deepseek_agents) {
            this.realThermalMemory.deepseek_agents = {};
        }

        this.realThermalMemory.deepseek_agents[this.agentId] = {
            agent_id: this.agentId,
            model: this.modelName,
            integration_source: 'DIRECT_SERVER_INTEGRATION',
            integration_timestamp: Date.now(),
            server_port: PORT,
            ollama_url: this.ollamaUrl,
            thermal_integration: {
                integrated: true,
                qi_boost: 100,
                neural_allocation: 3000000000,
                memory_zones: ['zone1_working', 'zone2_episodic', 'zone3_procedural', 'zone4_semantic'],
                status: 'ACTIVE_DIRECT_INTEGRATION'
            },
            capabilities: {
                thermal_memory_access: true,
                real_time_processing: true,
                neural_network_integration: true,
                conversation_memory: true
            }
        };

        // Mettre à jour le QI global
        if (this.realThermalMemory.neural_system && this.realThermalMemory.neural_system.qi_components) {
            this.realThermalMemory.neural_system.qi_components.deepseek_r1_direct_integration = 100;

            // Recalculer le QI total
            const totalQI = Object.values(this.realThermalMemory.neural_system.qi_components)
                .reduce((sum, value) => sum + value, 0);
            this.realThermalMemory.neural_system.qi_level = totalQI;
        }

        // Sauvegarder immédiatement
        this.saveThermalMemory();

        console.log('✅ DeepSeek R1 8B intégré dans la VRAIE mémoire thermique');
        console.log(`🧠 QI boost: +100`);
        console.log(`🧬 Neurones dédiés: 3,000,000,000`);
    }

    // Sauvegarder la mémoire thermique
    saveThermalMemory() {
        if (!this.realThermalMemory) return;

        try {
            // Backup avant sauvegarde
            const backupPath = `${thermalMemoryPath}.backup_deepseek_integration_${Date.now()}`;
            fs.copyFileSync(thermalMemoryPath, backupPath);

            // Sauvegarder
            fs.writeFileSync(thermalMemoryPath, JSON.stringify(this.realThermalMemory, null, 2));
            console.log(`💾 Mémoire thermique sauvegardée (backup: ${path.basename(backupPath)})`);
        } catch (error) {
            console.error('❌ Erreur sauvegarde mémoire thermique:', error.message);
        }
    }

    // Requête à DeepSeek avec contexte de mémoire thermique
    async queryWithThermalContext(prompt, conversationId = null) {
        const memoryContext = this.buildThermalContext();

        const fullPrompt = `Tu es DeepSeek R1 8B intégré dans la mémoire thermique de Jean-Luc.

CONTEXTE MÉMOIRE THERMIQUE:
${memoryContext}

INSTRUCTION: Réponds en utilisant ce contexte de mémoire thermique. Tu as accès à ${this.realThermalMemory?.neural_system?.total_neurons?.toLocaleString() || 'Unknown'} neurones et un QI de ${this.realThermalMemory?.neural_system?.qi_level || 'Unknown'}.

QUESTION: ${prompt}`;

        try {
            const response = await this.queryOllama(fullPrompt);

            // Enregistrer l'interaction dans la mémoire thermique
            this.recordInteraction(prompt, response, conversationId);

            return response;
        } catch (error) {
            console.error('❌ Erreur requête DeepSeek:', error.message);
            return `Erreur de connexion à DeepSeek R1 8B: ${error.message}`;
        }
    }

    // Construire le contexte de mémoire thermique
    buildThermalContext() {
        if (!this.realThermalMemory) {
            return "Mémoire thermique non disponible";
        }

        const neural = this.realThermalMemory.neural_system;
        const agents = this.realThermalMemory.deepseek_agents || {};

        let context = `QI Système: ${neural?.qi_level || 'Unknown'}
Neurones Totaux: ${neural?.total_neurons?.toLocaleString() || 'Unknown'}
Neurones Actifs: ${neural?.active_neurons?.toLocaleString() || 'Unknown'}
Efficacité Neuronale: ${neural?.neural_efficiency || 'Unknown'}

Agents DeepSeek Intégrés: ${Object.keys(agents).length}`;

        // Ajouter les zones mémoire chaudes
        if (neural?.brain_waves) {
            context += `\nOndes Cérébrales Dominantes: ${neural.brain_waves.current_dominant}`;
        }

        return context;
    }

    // Requête directe à Ollama
    async queryOllama(prompt) {
        const response = await fetch(`${this.ollamaUrl}/api/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: this.modelName,
                prompt: prompt,
                stream: false
            })
        });

        if (!response.ok) {
            throw new Error(`Ollama error: ${response.status}`);
        }

        const data = await response.json();
        return data.response;
    }

    // Enregistrer l'interaction dans la mémoire thermique
    recordInteraction(prompt, response, conversationId) {
        if (!this.realThermalMemory) return;

        // Ajouter à l'historique des interactions
        if (!this.realThermalMemory.deepseek_interactions) {
            this.realThermalMemory.deepseek_interactions = [];
        }

        const interaction = {
            timestamp: Date.now(),
            agent_id: this.agentId,
            conversation_id: conversationId,
            prompt: prompt.substring(0, 200) + (prompt.length > 200 ? '...' : ''),
            response: response.substring(0, 200) + (response.length > 200 ? '...' : ''),
            thermal_context_used: true
        };

        this.realThermalMemory.deepseek_interactions.push(interaction);

        // Garder seulement les 100 dernières interactions
        if (this.realThermalMemory.deepseek_interactions.length > 100) {
            this.realThermalMemory.deepseek_interactions =
                this.realThermalMemory.deepseek_interactions.slice(-100);
        }

        // Sauvegarder périodiquement (toutes les 10 interactions)
        if (this.realThermalMemory.deepseek_interactions.length % 10 === 0) {
            this.saveThermalMemory();
        }
    }

    // Obtenir le statut de l'intégration
    getIntegrationStatus() {
        if (!this.realThermalMemory || !this.realThermalMemory.deepseek_agents) {
            return { integrated: false, error: 'Mémoire thermique non disponible' };
        }

        const agent = this.realThermalMemory.deepseek_agents[this.agentId];
        if (!agent) {
            return { integrated: false, error: 'Agent non trouvé' };
        }

        return {
            integrated: true,
            agent_id: this.agentId,
            model: this.modelName,
            qi_boost: agent.thermal_integration.qi_boost,
            neural_allocation: agent.thermal_integration.neural_allocation,
            memory_zones: agent.thermal_integration.memory_zones,
            status: agent.thermal_integration.status,
            interactions_count: this.realThermalMemory.deepseek_interactions?.length || 0,
            total_qi: this.realThermalMemory.neural_system?.qi_level || 'Unknown'
        };
    }
}

// Initialiser DeepSeek R1 8B avec intégration thermique
const deepseekIntegration = new DeepSeekR1ThermalIntegration();

// Route pour la racine - rediriger vers l'interface Louna
app.get('/', (req, res) => {
  res.redirect('/louna');
});

// Route pour l'alias Luna (pour compatibilité avec les liens dans la barre latérale)
app.get('/luna', (req, res) => {
  res.redirect('/louna');
});

// Route principale
app.get('/louna', (req, res) => {
  res.render('luna-chat', {
    title: 'Louna - Interface Cognitive',
    page: 'chat'
  });
});

// Route pour la page d'accueil
app.get(['/louna/home', '/luna/home'], (req, res) => {
  res.render('luna-home', {
    title: 'Louna - Accueil',
    page: 'home'
  });
});

// Route pour la page de chat
app.get(['/louna/chat', '/luna/chat'], (req, res) => {
  res.render('luna-chat', {
    title: 'Louna - Chat',
    page: 'chat'
  });
});

// Route pour la page de mémoire
app.get(['/louna/memory', '/luna/memory'], (req, res) => {
  res.render('luna-memory', {
    title: 'Louna - Mémoire',
    page: 'memory'
  });
});

// Route pour la page de formation
app.get(['/louna/training', '/luna/training'], (req, res) => {
  res.render('luna-training', {
    title: 'Louna - Formation',
    page: 'training'
  });
});

// Route pour la page de code
app.get(['/louna/code', '/luna/code'], (req, res) => {
  res.render('luna-code', {
    title: 'Louna - Code',
    page: 'code'
  });
});

// Route pour la page de sécurité
app.get(['/louna/security', '/luna/security'], (req, res) => {
  res.render('luna-security', {
    title: 'Louna - Sécurité',
    page: 'security'
  });
});

// Route pour la page de sauvegarde
app.get(['/louna/backup', '/luna/backup'], (req, res) => {
  res.render('luna-backup', {
    title: 'Louna - Sauvegarde',
    page: 'backup'
  });
});

// Route pour la page de surveillance
app.get(['/louna/monitor', '/luna/monitor'], (req, res) => {
  res.render('luna-monitor', {
    title: 'Louna - Surveillance',
    page: 'monitor'
  });
});

// Route pour la page des accélérateurs
app.get(['/louna/accelerators', '/luna/accelerators'], (req, res) => {
  res.render('luna-accelerators', {
    title: 'Louna - Accélérateurs',
    page: 'accelerators'
  });
});

// Route pour la page des statistiques
app.get(['/louna/stats', '/luna/stats'], (req, res) => {
  res.render('luna-stats', {
    title: 'Louna - Statistiques',
    page: 'stats'
  });
});

// Route pour la page des paramètres
app.get(['/louna/settings', '/luna/settings'], (req, res) => {
  res.render('luna-settings', {
    title: 'Louna - Paramètres',
    page: 'settings'
  });
});

// Route pour la page des modèles
app.get(['/louna/models', '/luna/models'], (req, res) => {
  res.render('luna-models', {
    title: 'Louna - Modèles',
    page: 'models'
  });
});

// Route pour la page des documents
app.get(['/louna/documents', '/luna/documents'], (req, res) => {
  res.render('luna-documents', {
    title: 'Louna - Documents',
    page: 'documents'
  });
});

// Route pour la page des prompts
app.get(['/louna/prompts', '/luna/prompts'], (req, res) => {
  res.render('luna-prompts', {
    title: 'Louna - Prompts',
    page: 'prompts'
  });
});

// Route pour la page MCP
app.get(['/louna/mcp', '/luna/mcp'], (req, res) => {
  res.render('luna-mcp', {
    title: 'Louna - MCP',
    page: 'mcp'
  });
});

// Route pour la page Internet
app.get(['/louna/internet', '/luna/internet'], (req, res) => {
  res.render('luna-internet', {
    title: 'Louna - Internet',
    page: 'internet'
  });
});

// Route pour la page VPN
app.get(['/louna/vpn', '/luna/vpn'], (req, res) => {
  res.render('luna-vpn', {
    title: 'Louna - VPN',
    page: 'vpn'
  });
});

// Route pour la page Antivirus
app.get(['/louna/antivirus', '/luna/antivirus'], (req, res) => {
  res.render('luna-antivirus', {
    title: 'Louna - Antivirus',
    page: 'antivirus'
  });
});

// Route pour la page Cognitive
app.get(['/louna/cognitive', '/luna/cognitive'], (req, res) => {
  res.render('luna-cognitive', {
    title: 'Louna - Cognitive',
    page: 'cognitive'
  });
});

// Route pour la page de réflexion
app.get(['/louna/reflection', '/luna/reflection'], (req, res) => {
  res.render('luna-reflection', {
    title: 'Louna - Réflexion',
    page: 'reflection'
  });
});

// Route pour la page multimédia
app.get(['/louna/media', '/luna/media'], (req, res) => {
  res.render('luna-media', {
    title: 'Louna - Multimédia',
    page: 'media'
  });
});

// Route pour la page multimédia studio
app.get(['/louna/multimedia-studio', '/luna/multimedia-studio'], (req, res) => {
  res.render('luna-multimedia-studio', {
    title: 'Louna - Studio Multimédia',
    page: 'multimedia-studio'
  });
});

// Route pour la page multimedia (alias pour media)
app.get(['/louna/multimedia', '/luna/multimedia'], (req, res) => {
  res.render('luna-multimedia', {
    title: 'Louna - Multimédia',
    page: 'multimedia'
  });
});

// Route pour la page de connectivité
app.get(['/louna/connectivity', '/luna/connectivity'], (req, res) => {
  res.render('luna-connectivity', {
    title: 'Louna - Connectivité',
    page: 'connectivity'
  });
});

// Route pour la page cerveau
app.get(['/louna/brain', '/luna/brain'], (req, res) => {
  res.render('luna-brain', {
    title: 'Louna - Cerveau',
    page: 'brain'
  });
});

// Route pour la page présence
app.get(['/louna/presence', '/luna/presence'], (req, res) => {
  res.render('luna-presence', {
    title: 'Louna - Présence',
    page: 'presence'
  });
});

// Route pour la page présentation
app.get(['/louna/presentation', '/luna/presentation'], (req, res) => {
  res.render('luna-presentation', {
    title: 'Louna - Présentation',
    page: 'presentation'
  });
});

// Route pour la page code-fixer
app.get(['/louna/code-fixer', '/luna/code-fixer'], (req, res) => {
  res.render('luna-code-fixer', {
    title: 'Louna - Correcteur de Code',
    page: 'code-fixer'
  });
});

// Route pour la page accelerators-hierarchy
app.get(['/louna/accelerators-hierarchy', '/luna/accelerators-hierarchy'], (req, res) => {
  res.render('luna-accelerators-hierarchy', {
    title: 'Louna - Hiérarchie des Accélérateurs',
    page: 'accelerators-hierarchy'
  });
});

// Route pour la page accelerators-new
app.get(['/louna/accelerators-new', '/luna/accelerators-new'], (req, res) => {
  res.render('luna-accelerators-new', {
    title: 'Louna - Nouveaux Accélérateurs',
    page: 'accelerators-new'
  });
});

// Route pour la page accelerators-updated
app.get(['/louna/accelerators-updated', '/luna/accelerators-updated'], (req, res) => {
  res.render('luna-accelerators-updated', {
    title: 'Louna - Accélérateurs Mis à Jour',
    page: 'accelerators-updated'
  });
});

// Route pour la page thermal
app.get(['/louna/thermal', '/luna/thermal'], (req, res) => {
  res.render('luna-thermal', {
    title: 'Louna - Mémoire Thermique',
    page: 'thermal'
  });
});

// Route pour la page cognitive-fixed
app.get(['/louna/cognitive-fixed', '/luna/cognitive-fixed'], (req, res) => {
  res.render('luna-cognitive-fixed', {
    title: 'Louna - Cognitive Fixé',
    page: 'cognitive-fixed'
  });
});

// Route pour la page meeting
app.get(['/louna/meeting', '/luna/meeting'], (req, res) => {
  res.render('luna-meeting', {
    title: 'Louna - Réunion',
    page: 'meeting'
  });
});

// Route pour la page program-knowledge
app.get(['/louna/program-knowledge', '/luna/program-knowledge'], (req, res) => {
  res.render('luna-program-knowledge', {
    title: 'Louna - Connaissance des Programmes',
    page: 'program-knowledge'
  });
});

// Route pour la page sleep-mode
app.get(['/louna/sleep-mode', '/luna/sleep-mode'], (req, res) => {
  res.render('luna-sleep-mode', {
    title: 'Louna - Mode Veille',
    page: 'sleep-mode'
  });
});

// Route pour la page vscode
app.get(['/louna/vscode', '/luna/vscode'], (req, res) => {
  res.render('luna-vscode', {
    title: 'Louna - VS Code',
    page: 'vscode'
  });
});

// ROUTES API DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE
app.post('/api/deepseek/query', async (req, res) => {
    try {
        const { prompt, conversation_id } = req.body;

        if (!prompt) {
            return res.status(400).json({ error: 'Prompt requis' });
        }

        console.log(`🤖 Requête DeepSeek R1 8B: ${prompt.substring(0, 50)}...`);

        const response = await deepseekIntegration.queryWithThermalContext(prompt, conversation_id);

        res.json({
            success: true,
            response: response,
            agent_id: deepseekIntegration.agentId,
            thermal_context_used: true,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ Erreur API DeepSeek:', error.message);
        res.status(500).json({
            error: 'Erreur interne DeepSeek',
            details: error.message
        });
    }
});

app.get('/api/deepseek/status', (req, res) => {
    try {
        const status = deepseekIntegration.getIntegrationStatus();
        res.json(status);
    } catch (error) {
        res.status(500).json({
            error: 'Erreur récupération statut',
            details: error.message
        });
    }
});

app.get('/api/deepseek/thermal-memory', (req, res) => {
    try {
        if (!realThermalMemory) {
            return res.status(404).json({ error: 'Mémoire thermique non disponible' });
        }

        const memoryInfo = {
            qi_level: realThermalMemory.neural_system?.qi_level || 'Unknown',
            total_neurons: realThermalMemory.neural_system?.total_neurons || 'Unknown',
            active_neurons: realThermalMemory.neural_system?.active_neurons || 'Unknown',
            deepseek_agents: Object.keys(realThermalMemory.deepseek_agents || {}).length,
            interactions_count: realThermalMemory.deepseek_interactions?.length || 0,
            brain_waves: realThermalMemory.neural_system?.brain_waves?.current_dominant || 'Unknown'
        };

        res.json({
            success: true,
            thermal_memory: memoryInfo
        });

    } catch (error) {
        res.status(500).json({
            error: 'Erreur accès mémoire thermique',
            details: error.message
        });
    }
});

// Route pour forcer la sauvegarde de la mémoire thermique
app.post('/api/deepseek/save-thermal', (req, res) => {
    try {
        deepseekIntegration.saveThermalMemory();
        res.json({
            success: true,
            message: 'Mémoire thermique sauvegardée'
        });
    } catch (error) {
        res.status(500).json({
            error: 'Erreur sauvegarde',
            details: error.message
        });
    }
});

// Gestionnaire de socket pour les messages
io.on('connection', (socket) => {
  console.log('Nouvelle connexion WebSocket');

  // Vérifier l'état de la connexion à l'agent
  socket.on('check agent connection', () => {
    const isConnected = brainPresence.isConnected();
    socket.emit('agent connection status', { connected: isConnected });
    console.log(`Statut de connexion de l'agent vérifié: ${isConnected ? 'Connecté' : 'Déconnecté'}`);
  });

  // Gérer les messages de l'utilisateur avec DeepSeek R1 8B intégré
  socket.on('louna message', async (data) => {
    console.log('Message reçu:', data.message);

    // 1. Stocker le message dans la mémoire thermique locale
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });

    try {
      // 2. Utiliser DeepSeek R1 8B avec mémoire thermique RÉELLE
      console.log('🤖 Traitement avec DeepSeek R1 8B + Mémoire Thermique...');

      const deepseekResponse = await deepseekIntegration.queryWithThermalContext(
        data.message,
        data.conversation_id || `conv_${Date.now()}`
      );

      // 3. Former la réponse avec contexte thermal
      const response = {
        message: `🧠 DeepSeek R1 8B (Mémoire Thermique): ${deepseekResponse}`,
        timestamp: new Date().toISOString(),
        usedThermalMemory: true,
        agent_id: deepseekIntegration.agentId,
        qi_level: realThermalMemory?.neural_system?.qi_level || 'Unknown'
      };

      // 4. Stocker la réponse dans la mémoire thermique locale
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });

      // 5. Envoyer la réponse
      socket.emit('louna response', response);

    } catch (error) {
      console.error('❌ Erreur DeepSeek:', error.message);

      // Fallback vers le système original
      const isAgentConnected = brainPresence.isConnected();
      if (!isAgentConnected) {
        socket.emit('louna response', {
          message: "❌ DeepSeek R1 8B non disponible et système cognitif déconnecté.",
          timestamp: new Date().toISOString(),
          error: true
        });
        return;
      }

      // Fallback vers le système original si DeepSeek échoue
      console.log('⚠️ Utilisation du système de fallback...');
    }

    // 3. Rechercher dans la mémoire si elle contient déjà la réponse
    const memoryResult = await brainPresence.searchMemory(data.message);
    let responseMessage = '';
    let useMemory = false;
    let needInternet = false;

    if (memoryResult && memoryResult.relevance > 0.75) {
      // Utiliser la mémoire si la réponse est pertinente
      responseMessage = `<think>J'ai trouvé une réponse pertinente dans ma mémoire. Relevance: ${memoryResult.relevance}</think>\n${memoryResult.content}`;
      useMemory = true;
    } else {
      // Si la mémoire ne contient pas de réponse pertinente, vérifier si besoin d'Internet
      needInternet = brainPresence.needsInternetSearch(data.message);
      
      if (needInternet) {
        // Notifier que la recherche Internet est en cours
        socket.emit('agent status', { status: 'searching', message: 'Recherche en cours sur Internet...' });
        
        try {
          // Effectuer la recherche Internet
          const internetResult = await brainPresence.searchInternet(data.message);
          responseMessage = `<think>Je n'ai pas trouvé de réponse dans ma mémoire, j'ai donc cherché sur Internet.</think>\n${internetResult}`;
          
          // Stocker la nouvelle information dans la mémoire
          thermalMemory.addEntry({
            type: 'knowledge',
            query: data.message,
            content: internetResult,
            source: 'internet',
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Erreur lors de la recherche Internet:', error);
          responseMessage = `Je n'ai pas pu effectuer la recherche Internet. Erreur: ${error.message}`;
        }
      } else {
        // Générer une réponse basée sur le modèle cognitif sans mémoire spécifique ni Internet
        responseMessage = `<think>Je n'ai pas de souvenir pertinent sur ce sujet et je n'ai pas besoin d'Internet pour répondre.</think>\nJe vais répondre à votre question sur "${data.message}" en utilisant mes connaissances générales.`;
      }
    }

    // Former la réponse finale
    const response = {
      message: responseMessage,
      timestamp: new Date().toISOString(),
      usedMemory: useMemory,
      usedInternet: needInternet
    };

    // Stocker la réponse dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'output',
      content: response.message,
      timestamp: response.timestamp
    });

    // Envoyer la réponse au client
    socket.emit('louna response', response);
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Déconnexion WebSocket');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur Louna démarré sur le port ${PORT}`);
  console.log(`Interface accessible à l'adresse http://localhost:${PORT}/louna`);
});
