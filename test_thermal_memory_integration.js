#!/usr/bin/env node

/**
 * 🔥 TEST D'INTÉGRATION MÉMOIRE THERMIQUE
 * 
 * Vérification que l'agent utilise vraiment la mémoire thermique
 * Test authentique - pas de simulation
 * 
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');
const { spawn } = require('child_process');

class ThermalMemoryTester {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.testResults = [];
    }

    async testThermalMemoryIntegration() {
        console.log('🔥 TEST D\'INTÉGRATION MÉMOIRE THERMIQUE');
        console.log('=====================================');
        console.log('🎯 Vérification que l\'agent utilise vraiment la mémoire');
        
        try {
            // 1. Vérifier l'état de la mémoire thermique
            console.log('\n📚 Étape 1: Analyse de la mémoire thermique...');
            const memoryState = await this.analyzeThermalMemory();
            
            // 2. Ajouter une information test dans la mémoire
            console.log('\n🧪 Étape 2: Injection d\'information test...');
            const testInfo = await this.injectTestInformation();
            
            // 3. Tester si l'agent peut accéder à cette information
            console.log('\n🤖 Étape 3: Test d\'accès par l\'agent...');
            const agentResponse = await this.testAgentMemoryAccess(testInfo);
            
            // 4. Vérifier la persistance
            console.log('\n💾 Étape 4: Test de persistance...');
            const persistenceTest = await this.testMemoryPersistence();
            
            // 5. Générer le rapport
            const report = this.generateMemoryTestReport(memoryState, testInfo, agentResponse, persistenceTest);
            
            console.log('\n📊 RÉSULTATS DU TEST');
            console.log('=====================================');
            console.log(`🧠 QI système: ${memoryState.qi_level}`);
            console.log(`🔥 Zone DeepSeek: ${memoryState.hasDeepSeekZone ? 'ACTIVE' : 'INACTIVE'}`);
            console.log(`📝 Info test injectée: ${testInfo.success ? 'OUI' : 'NON'}`);
            console.log(`🤖 Agent accède mémoire: ${agentResponse.canAccess ? 'OUI' : 'NON'}`);
            console.log(`💾 Persistance: ${persistenceTest.persistent ? 'OUI' : 'NON'}`);
            
            const overallSuccess = memoryState.hasDeepSeekZone && testInfo.success && persistenceTest.persistent;
            
            console.log(`\n${overallSuccess ? '🎉' : '❌'} VERDICT: ${overallSuccess ? 'MÉMOIRE THERMIQUE FONCTIONNELLE' : 'PROBLÈME DÉTECTÉ'}`);
            
            return report;
            
        } catch (error) {
            console.error('\n❌ ERREUR LORS DU TEST');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async analyzeThermalMemory() {
        console.log('🔍 Analyse de l\'état actuel de la mémoire thermique...');
        
        if (!fs.existsSync(this.thermalMemoryPath)) {
            throw new Error('Fichier mémoire thermique non trouvé');
        }
        
        const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        const analysis = {
            file_exists: true,
            qi_level: thermalData.neural_system?.qi_level || 0,
            total_neurons: thermalData.neural_system?.total_neurons || 0,
            hasDeepSeekZone: !!(thermalData.thermal_zones?.zone_deepseek_r1_authentic),
            zones_count: Object.keys(thermalData.thermal_zones || {}).length,
            deepseek_integration: thermalData.neural_system?.deepseek_r1_authentic_integration?.active || false
        };
        
        console.log(`✅ QI système: ${analysis.qi_level}`);
        console.log(`✅ Neurones: ${analysis.total_neurons.toLocaleString()}`);
        console.log(`✅ Zones thermiques: ${analysis.zones_count}`);
        console.log(`✅ Zone DeepSeek: ${analysis.hasDeepSeekZone ? 'TROUVÉE' : 'MANQUANTE'}`);
        console.log(`✅ Intégration DeepSeek: ${analysis.deepseek_integration ? 'ACTIVE' : 'INACTIVE'}`);
        
        return analysis;
    }

    async injectTestInformation() {
        console.log('💉 Injection d\'information test dans la mémoire...');
        
        try {
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            // Créer une zone de test
            if (!thermalData.thermal_zones.zone_test_integration) {
                thermalData.thermal_zones.zone_test_integration = {
                    temperature: 37.0,
                    description: 'Zone de test d\'intégration mémoire',
                    entries: []
                };
            }
            
            // Ajouter une information test unique
            const testTimestamp = Date.now();
            const testEntry = {
                id: `test_memory_${testTimestamp}`,
                content: `TEST MÉMOIRE THERMIQUE - Jean-Luc a testé l'intégration le ${new Date().toISOString()}. Code secret: THERMAL_${testTimestamp}. Cette information doit être accessible par DeepSeek R1 8B.`,
                importance: 1.0,
                timestamp: Math.floor(testTimestamp / 1000),
                synaptic_strength: 1.0,
                temperature: 37.0,
                zone: 'zone_test_integration',
                source: 'thermal_memory_tester',
                type: 'test_information',
                test_code: `THERMAL_${testTimestamp}`
            };
            
            thermalData.thermal_zones.zone_test_integration.entries.push(testEntry);
            
            // Sauvegarder
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalData, null, 2));
            
            console.log(`✅ Information test injectée avec code: THERMAL_${testTimestamp}`);
            
            return {
                success: true,
                test_code: `THERMAL_${testTimestamp}`,
                timestamp: testTimestamp,
                entry_id: testEntry.id
            };
            
        } catch (error) {
            console.log(`❌ Erreur injection: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    async testAgentMemoryAccess(testInfo) {
        console.log('🤖 Test d\'accès à la mémoire par l\'agent...');
        
        if (!testInfo.success) {
            return { canAccess: false, reason: 'Test info injection failed' };
        }
        
        try {
            // Créer un prompt qui demande à l'agent d'accéder à sa mémoire
            const memoryPrompt = `Tu es DeepSeek R1 8B intégré dans la mémoire thermique de Jean-Luc. Peux-tu me dire quel est ton niveau de QI actuel et si tu as accès à des informations sur des tests récents ? Cherche dans ta mémoire thermique.`;
            
            console.log('📝 Question à l\'agent sur sa mémoire...');
            const response = await this.queryDeepSeek(memoryPrompt);
            
            if (response) {
                console.log(`📝 Réponse agent: "${response.substring(0, 200)}..."`);
                
                // Analyser la réponse pour voir s'il mentionne des éléments de la mémoire
                const mentionsQI = response.toLowerCase().includes('qi') || response.includes('711') || response.includes('700');
                const mentionsMemory = response.toLowerCase().includes('mémoire') || response.toLowerCase().includes('memory') || response.toLowerCase().includes('thermal');
                const mentionsTest = response.toLowerCase().includes('test') || response.includes(testInfo.test_code);
                
                return {
                    canAccess: mentionsQI || mentionsMemory,
                    response: response.substring(0, 500),
                    mentions_qi: mentionsQI,
                    mentions_memory: mentionsMemory,
                    mentions_test: mentionsTest,
                    test_code_found: response.includes(testInfo.test_code)
                };
            } else {
                return { canAccess: false, reason: 'No response from agent' };
            }
            
        } catch (error) {
            console.log(`❌ Erreur test agent: ${error.message}`);
            return { canAccess: false, error: error.message };
        }
    }

    async testMemoryPersistence() {
        console.log('💾 Test de persistance de la mémoire...');
        
        try {
            // Vérifier que le fichier existe toujours
            const exists = fs.existsSync(this.thermalMemoryPath);
            
            if (!exists) {
                return { persistent: false, reason: 'Memory file disappeared' };
            }
            
            // Vérifier que les données sont toujours là
            const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            const hasDeepSeekZone = !!(thermalData.thermal_zones?.zone_deepseek_r1_authentic);
            const hasTestZone = !!(thermalData.thermal_zones?.zone_test_integration);
            const qiLevel = thermalData.neural_system?.qi_level || 0;
            
            console.log(`✅ Fichier existe: ${exists}`);
            console.log(`✅ Zone DeepSeek: ${hasDeepSeekZone}`);
            console.log(`✅ Zone test: ${hasTestZone}`);
            console.log(`✅ QI niveau: ${qiLevel}`);
            
            return {
                persistent: exists && hasDeepSeekZone && qiLevel > 700,
                file_exists: exists,
                deepseek_zone: hasDeepSeekZone,
                test_zone: hasTestZone,
                qi_level: qiLevel
            };
            
        } catch (error) {
            console.log(`❌ Erreur persistance: ${error.message}`);
            return { persistent: false, error: error.message };
        }
    }

    async queryDeepSeek(question) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', question]);
            
            let output = '';
            let errorOutput = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            ollama.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            // Timeout après 60 secondes
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, 60000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                if (code === 0 && output.trim()) {
                    // Nettoyer la sortie
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    generateMemoryTestReport(memoryState, testInfo, agentResponse, persistenceTest) {
        const report = {
            timestamp: Date.now(),
            test_type: 'thermal_memory_integration',
            memory_analysis: memoryState,
            test_injection: testInfo,
            agent_access_test: agentResponse,
            persistence_test: persistenceTest,
            overall_success: memoryState.hasDeepSeekZone && testInfo.success && persistenceTest.persistent,
            recommendations: []
        };
        
        // Ajouter des recommandations
        if (!memoryState.hasDeepSeekZone) {
            report.recommendations.push('Réintégrer DeepSeek dans la mémoire thermique');
        }
        
        if (!testInfo.success) {
            report.recommendations.push('Vérifier les permissions d\'écriture du fichier mémoire');
        }
        
        if (!agentResponse.canAccess) {
            report.recommendations.push('L\'agent ne semble pas accéder à sa mémoire thermique');
        }
        
        if (!persistenceTest.persistent) {
            report.recommendations.push('Problème de persistance de la mémoire');
        }
        
        // Sauvegarder le rapport
        const reportPath = `thermal_memory_test_report_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📋 Rapport détaillé: ${reportPath}`);
        
        return report;
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🔥 TEST D\'INTÉGRATION MÉMOIRE THERMIQUE');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Vérification authentique de l\'utilisation de la mémoire');
    
    const tester = new ThermalMemoryTester();
    await tester.testThermalMemoryIntegration();
}

if (require.main === module) {
    main();
}

module.exports = ThermalMemoryTester;
