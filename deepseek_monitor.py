#!/usr/bin/env python3
"""
DeepSeek Real-time Monitor
System monitoring and performance tracking
Jean-Luc PASSAVE - 2025
"""

import json
import time
import threading
import os
from datetime import datetime
from typing import Dict, List, Any
import logging

class SystemMonitor:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.monitoring = False
        self.monitor_thread = None
        self.metrics_history = []
        self.max_history = 100
        self.lock = threading.Lock()
        
    def start_monitoring(self, interval: float = 1.0):
        """Start real-time monitoring"""
        if self.monitoring:
            return False
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        return True
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
    
    def _monitor_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                
                with self.lock:
                    self.metrics_history.append(metrics)
                    if len(self.metrics_history) > self.max_history:
                        self.metrics_history.pop(0)
                
                time.sleep(interval)
                
            except Exception as e:
                logging.error(f"Monitor error: {e}")
                time.sleep(interval)
    
    def _collect_metrics(self) -> Dict[str, Any]:
        """Collect system metrics"""
        timestamp = time.time()
        
        try:
            # File stats
            stat = os.stat(self.memory_file)
            file_size = stat.st_size
            last_modified = stat.st_mtime
            
            # Load memory data
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            # Neural system metrics
            neural = memory_data.get('neural_system', {})
            qi_level = neural.get('qi_level', 0)
            active_neurons = neural.get('active_neurons', 0)
            total_neurons = neural.get('total_neurons', 0)
            cardiac_bpm = neural.get('cardiac_rhythm', {}).get('bpm', 0)
            
            # Thermal zones metrics
            thermal_zones = memory_data.get('thermal_zones', {})
            zone_count = len(thermal_zones)
            
            hot_zones = []
            total_entries = 0
            avg_temperature = 0
            max_temperature = 0
            
            for zone_name, zone_data in thermal_zones.items():
                temp = zone_data.get('temperature', 0)
                entries = len(zone_data.get('entries', []))
                total_entries += entries
                
                if temp >= 80:
                    hot_zones.append({
                        'name': zone_name,
                        'temperature': temp,
                        'entries': entries
                    })
                
                avg_temperature += temp
                max_temperature = max(max_temperature, temp)
            
            avg_temperature = avg_temperature / zone_count if zone_count > 0 else 0
            
            # Performance calculations
            efficiency_ratio = (active_neurons / total_neurons * 100) if total_neurons > 0 else 0
            
            return {
                'timestamp': timestamp,
                'datetime': datetime.fromtimestamp(timestamp).isoformat(),
                'file_size_mb': round(file_size / (1024 * 1024), 2),
                'last_modified': last_modified,
                'qi_level': qi_level,
                'active_neurons': active_neurons,
                'total_neurons': total_neurons,
                'efficiency_ratio': round(efficiency_ratio, 2),
                'cardiac_bpm': cardiac_bpm,
                'zone_count': zone_count,
                'hot_zones_count': len(hot_zones),
                'total_entries': total_entries,
                'avg_temperature': round(avg_temperature, 2),
                'max_temperature': max_temperature,
                'hot_zones': hot_zones[:5]  # Top 5 hot zones
            }
            
        except Exception as e:
            return {
                'timestamp': timestamp,
                'error': str(e)
            }
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        with self.lock:
            return self.metrics_history[-1] if self.metrics_history else {}
    
    def get_metrics_history(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get recent metrics history"""
        with self.lock:
            return self.metrics_history[-count:] if self.metrics_history else []
    
    def get_performance_trend(self) -> Dict[str, Any]:
        """Calculate performance trends"""
        with self.lock:
            if len(self.metrics_history) < 2:
                return {}
            
            recent = self.metrics_history[-10:]  # Last 10 measurements
            
            # Calculate trends
            qi_values = [m.get('qi_level', 0) for m in recent if 'qi_level' in m]
            efficiency_values = [m.get('efficiency_ratio', 0) for m in recent if 'efficiency_ratio' in m]
            bpm_values = [m.get('cardiac_bpm', 0) for m in recent if 'cardiac_bpm' in m]
            temp_values = [m.get('max_temperature', 0) for m in recent if 'max_temperature' in m]
            
            def calculate_trend(values):
                if len(values) < 2:
                    return 0
                return values[-1] - values[0]
            
            return {
                'qi_trend': calculate_trend(qi_values),
                'efficiency_trend': round(calculate_trend(efficiency_values), 2),
                'bpm_trend': calculate_trend(bpm_values),
                'temperature_trend': round(calculate_trend(temp_values), 2),
                'measurements': len(recent)
            }

class PerformanceAnalyzer:
    def __init__(self, monitor: SystemMonitor):
        self.monitor = monitor
        
    def analyze_performance(self) -> Dict[str, Any]:
        """Analyze current performance"""
        current = self.monitor.get_current_metrics()
        trends = self.monitor.get_performance_trend()
        
        if not current:
            return {'status': 'no_data'}
        
        # Performance scoring
        qi_score = min(current.get('qi_level', 0) / 1000, 1.0)  # Normalize to 1.0
        efficiency_score = current.get('efficiency_ratio', 0) / 100
        thermal_score = min(current.get('max_temperature', 0) / 1200, 1.0)
        
        overall_score = (qi_score + efficiency_score + thermal_score) / 3
        
        # Status determination
        if overall_score >= 0.8:
            status = 'excellent'
        elif overall_score >= 0.6:
            status = 'good'
        elif overall_score >= 0.4:
            status = 'average'
        else:
            status = 'poor'
        
        return {
            'status': status,
            'overall_score': round(overall_score, 3),
            'qi_score': round(qi_score, 3),
            'efficiency_score': round(efficiency_score, 3),
            'thermal_score': round(thermal_score, 3),
            'current_metrics': current,
            'trends': trends
        }
    
    def generate_report(self) -> str:
        """Generate performance report"""
        analysis = self.analyze_performance()
        
        if analysis.get('status') == 'no_data':
            return "No performance data available"
        
        current = analysis['current_metrics']
        trends = analysis['trends']
        
        report = f"""DEEPSEEK PERFORMANCE REPORT
Generated: {current.get('datetime', 'Unknown')}

OVERALL STATUS: {analysis['status'].upper()}
Overall Score: {analysis['overall_score']:.3f}

NEURAL SYSTEM:
- QI Level: {current.get('qi_level', 'Unknown')}
- Active Neurons: {current.get('active_neurons', 'Unknown'):,}
- Efficiency: {current.get('efficiency_ratio', 'Unknown')}%
- Cardiac BPM: {current.get('cardiac_bpm', 'Unknown')}

THERMAL SYSTEM:
- Total Zones: {current.get('zone_count', 'Unknown')}
- Hot Zones (≥80°C): {current.get('hot_zones_count', 'Unknown')}
- Max Temperature: {current.get('max_temperature', 'Unknown')}°C
- Avg Temperature: {current.get('avg_temperature', 'Unknown')}°C

MEMORY SYSTEM:
- File Size: {current.get('file_size_mb', 'Unknown')} MB
- Total Entries: {current.get('total_entries', 'Unknown')}

PERFORMANCE TRENDS:
- QI Trend: {trends.get('qi_trend', 0):+.0f}
- Efficiency Trend: {trends.get('efficiency_trend', 0):+.2f}%
- BPM Trend: {trends.get('bpm_trend', 0):+.0f}
- Temperature Trend: {trends.get('temperature_trend', 0):+.2f}°C

TOP HOT ZONES:"""
        
        hot_zones = current.get('hot_zones', [])
        for i, zone in enumerate(hot_zones, 1):
            report += f"""
{i}. {zone['name']}: {zone['temperature']}°C ({zone['entries']} entries)"""
        
        return report

def main():
    logging.basicConfig(level=logging.INFO)
    
    memory_file = "./thermal_memory_real_clones_1749979850296.json"
    
    # Initialize monitor
    monitor = SystemMonitor(memory_file)
    analyzer = PerformanceAnalyzer(monitor)
    
    print("DeepSeek Real-time Monitor")
    print("=========================")
    
    # Start monitoring
    if monitor.start_monitoring(interval=2.0):
        print("Monitoring started (2s interval)")
    else:
        print("Failed to start monitoring")
        return 1
    
    # Wait for initial data
    time.sleep(3)
    
    print("\nCommands: /status, /report, /metrics, /trends, /quit")
    
    try:
        while True:
            user_input = input("\nMonitor> ")
            
            if user_input in ['/quit', '/exit']:
                break
            
            elif user_input == '/status':
                analysis = analyzer.analyze_performance()
                if analysis.get('status') != 'no_data':
                    print(f"\nStatus: {analysis['status'].upper()}")
                    print(f"Overall Score: {analysis['overall_score']:.3f}")
                    print(f"QI: {analysis['current_metrics'].get('qi_level', 'Unknown')}")
                    print(f"Efficiency: {analysis['current_metrics'].get('efficiency_ratio', 'Unknown')}%")
                    print(f"Hot Zones: {analysis['current_metrics'].get('hot_zones_count', 'Unknown')}")
                else:
                    print("No data available")
            
            elif user_input == '/report':
                report = analyzer.generate_report()
                print(f"\n{report}")
            
            elif user_input == '/metrics':
                current = monitor.get_current_metrics()
                if current:
                    print(f"\nCurrent Metrics:")
                    for key, value in current.items():
                        if key not in ['hot_zones', 'datetime']:
                            print(f"  {key}: {value}")
                else:
                    print("No metrics available")
            
            elif user_input == '/trends':
                trends = monitor.get_performance_trend()
                if trends:
                    print(f"\nPerformance Trends:")
                    for key, value in trends.items():
                        print(f"  {key}: {value}")
                else:
                    print("No trend data available")
            
            else:
                print("Unknown command")
    
    except KeyboardInterrupt:
        pass
    
    finally:
        print("\nStopping monitor...")
        monitor.stop_monitoring()
        print("Monitor stopped")
    
    return 0

if __name__ == "__main__":
    exit(main())
