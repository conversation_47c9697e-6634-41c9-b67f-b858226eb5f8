#!/usr/bin/env python3
"""
🧠 SERVEUR MCP MÉMOIRE THERMIQUE
Connexion directe DeepSeek <-> Mémoire Thermique
<PERSON> PASSAVE - 2025
"""

import json
import asyncio
import logging
from typing import Any, Dict, List, Optional
from dataclasses import dataclass
from pathlib import Path

# Configuration MCP
@dataclass
class MCPConfig:
    memory_file: str = "./thermal_memory_real_clones_1749979850296.json"
    server_name: str = "thermal-memory-server"
    version: str = "1.0.0"
    port: int = 8080

class ThermalMemoryMCP:
    def __init__(self, config: MCPConfig):
        self.config = config
        self.memory_data = None
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger("ThermalMemoryMCP")
    
    def load_memory(self) -> bool:
        """Charge la mémoire thermique"""
        try:
            with open(self.config.memory_file, 'r', encoding='utf-8') as f:
                self.memory_data = json.load(f)
            
            zones_count = len(self.memory_data.get('thermal_zones', {}))
            self.logger.info(f"Mémoire chargée: {zones_count} zones thermiques")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur chargement mémoire: {e}")
            return False
    
    def get_neural_system(self) -> Dict[str, Any]:
        """Récupère les données du système neural"""
        if not self.memory_data:
            return {}
        
        neural = self.memory_data.get('neural_system', {})
        return {
            'qi_level': neural.get('qi_level', 0),
            'active_neurons': neural.get('active_neurons', 0),
            'total_neurons': neural.get('total_neurons', 0),
            'neural_efficiency': neural.get('neural_efficiency', ''),
            'architecture_version': neural.get('architecture_version', '')
        }
    
    def get_thermal_zones(self) -> Dict[str, Any]:
        """Récupère les zones thermiques"""
        if not self.memory_data:
            return {}
        
        zones = self.memory_data.get('thermal_zones', {})
        zone_summary = {}
        
        for zone_name, zone_data in zones.items():
            zone_summary[zone_name] = {
                'temperature': zone_data.get('temperature', 0),
                'description': zone_data.get('description', ''),
                'entries_count': len(zone_data.get('entries', [])),
                'active': zone_data.get('active', True)
            }
        
        return zone_summary
    
    def search_memory(self, query: str) -> List[Dict[str, Any]]:
        """Recherche dans la mémoire thermique"""
        if not self.memory_data:
            return []
        
        results = []
        query_lower = query.lower()
        
        # Recherche dans les zones thermiques
        zones = self.memory_data.get('thermal_zones', {})
        for zone_name, zone_data in zones.items():
            
            # Recherche dans la description de la zone
            if query_lower in zone_data.get('description', '').lower():
                results.append({
                    'type': 'zone_description',
                    'zone': zone_name,
                    'temperature': zone_data.get('temperature', 0),
                    'content': zone_data.get('description', ''),
                    'relevance': 'high'
                })
            
            # Recherche dans les entrées
            entries = zone_data.get('entries', [])
            for entry in entries:
                content = entry.get('content', '')
                if query_lower in content.lower():
                    results.append({
                        'type': 'zone_entry',
                        'zone': zone_name,
                        'temperature': zone_data.get('temperature', 0),
                        'entry_id': entry.get('id', ''),
                        'content': content,
                        'importance': entry.get('importance', 0),
                        'timestamp': entry.get('timestamp', 0),
                        'relevance': 'medium'
                    })
        
        # Trier par pertinence et température
        results.sort(key=lambda x: (x.get('relevance') == 'high', x.get('temperature', 0)), reverse=True)
        return results[:10]  # Limiter à 10 résultats
    
    def get_hot_zones(self, min_temp: float = 80.0) -> List[Dict[str, Any]]:
        """Récupère les zones chaudes (importantes)"""
        if not self.memory_data:
            return []
        
        hot_zones = []
        zones = self.memory_data.get('thermal_zones', {})
        
        for zone_name, zone_data in zones.items():
            temp = zone_data.get('temperature', 0)
            if temp >= min_temp:
                hot_zones.append({
                    'zone': zone_name,
                    'temperature': temp,
                    'description': zone_data.get('description', ''),
                    'entries_count': len(zone_data.get('entries', [])),
                    'last_activity': self._get_last_activity(zone_data)
                })
        
        # Trier par température décroissante
        hot_zones.sort(key=lambda x: x['temperature'], reverse=True)
        return hot_zones
    
    def _get_last_activity(self, zone_data: Dict[str, Any]) -> int:
        """Récupère le timestamp de la dernière activité"""
        entries = zone_data.get('entries', [])
        if not entries:
            return 0
        
        timestamps = [entry.get('timestamp', 0) for entry in entries]
        return max(timestamps) if timestamps else 0
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Statistiques de la mémoire"""
        if not self.memory_data:
            return {}
        
        zones = self.memory_data.get('thermal_zones', {})
        total_entries = sum(len(zone.get('entries', [])) for zone in zones.values())
        
        temps = [zone.get('temperature', 0) for zone in zones.values()]
        avg_temp = sum(temps) / len(temps) if temps else 0
        max_temp = max(temps) if temps else 0
        
        neural = self.memory_data.get('neural_system', {})
        
        return {
            'total_zones': len(zones),
            'total_entries': total_entries,
            'average_temperature': round(avg_temp, 2),
            'max_temperature': max_temp,
            'qi_level': neural.get('qi_level', 0),
            'active_neurons': neural.get('active_neurons', 0),
            'memory_size_mb': self._get_file_size_mb(),
            'last_modified': self.memory_data.get('last_modified', '')
        }
    
    def _get_file_size_mb(self) -> float:
        """Taille du fichier mémoire en MB"""
        try:
            size_bytes = Path(self.config.memory_file).stat().st_size
            return round(size_bytes / (1024 * 1024), 2)
        except:
            return 0.0

class MCPServer:
    def __init__(self, thermal_memory: ThermalMemoryMCP):
        self.thermal_memory = thermal_memory
        self.logger = logging.getLogger("MCPServer")
    
    async def handle_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Traite les requêtes MCP"""
        
        if method == "memory/neural_system":
            return {
                "success": True,
                "data": self.thermal_memory.get_neural_system()
            }
        
        elif method == "memory/thermal_zones":
            return {
                "success": True,
                "data": self.thermal_memory.get_thermal_zones()
            }
        
        elif method == "memory/search":
            query = params.get('query', '')
            if not query:
                return {"success": False, "error": "Query required"}
            
            results = self.thermal_memory.search_memory(query)
            return {
                "success": True,
                "data": {
                    "query": query,
                    "results": results,
                    "count": len(results)
                }
            }
        
        elif method == "memory/hot_zones":
            min_temp = params.get('min_temperature', 80.0)
            hot_zones = self.thermal_memory.get_hot_zones(min_temp)
            return {
                "success": True,
                "data": {
                    "hot_zones": hot_zones,
                    "count": len(hot_zones),
                    "min_temperature": min_temp
                }
            }
        
        elif method == "memory/stats":
            return {
                "success": True,
                "data": self.thermal_memory.get_memory_stats()
            }
        
        elif method == "memory/reload":
            success = self.thermal_memory.load_memory()
            return {
                "success": success,
                "message": "Memory reloaded" if success else "Failed to reload memory"
            }
        
        else:
            return {
                "success": False,
                "error": f"Unknown method: {method}"
            }

def create_mcp_tools() -> List[Dict[str, Any]]:
    """Définit les outils MCP disponibles"""
    return [
        {
            "name": "get_neural_system",
            "description": "Récupère les informations du système neural (QI, neurones, etc.)",
            "inputSchema": {
                "type": "object",
                "properties": {},
                "required": []
            }
        },
        {
            "name": "get_thermal_zones", 
            "description": "Récupère la liste des zones thermiques avec températures",
            "inputSchema": {
                "type": "object",
                "properties": {},
                "required": []
            }
        },
        {
            "name": "search_memory",
            "description": "Recherche dans la mémoire thermique par mots-clés",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Terme à rechercher dans la mémoire"
                    }
                },
                "required": ["query"]
            }
        },
        {
            "name": "get_hot_zones",
            "description": "Récupère les zones chaudes (importantes) au-dessus d'une température",
            "inputSchema": {
                "type": "object", 
                "properties": {
                    "min_temperature": {
                        "type": "number",
                        "description": "Température minimale (défaut: 80.0)",
                        "default": 80.0
                    }
                },
                "required": []
            }
        },
        {
            "name": "get_memory_stats",
            "description": "Statistiques générales de la mémoire thermique",
            "inputSchema": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    ]

async def main():
    """Point d'entrée principal"""
    config = MCPConfig()
    thermal_memory = ThermalMemoryMCP(config)
    
    # Charger la mémoire
    if not thermal_memory.load_memory():
        print("❌ Impossible de charger la mémoire thermique")
        return
    
    # Créer le serveur MCP
    server = MCPServer(thermal_memory)
    
    print("🧠 SERVEUR MCP MÉMOIRE THERMIQUE")
    print("================================")
    print(f"📁 Fichier mémoire: {config.memory_file}")
    print(f"🌡️ Zones thermiques: {len(thermal_memory.memory_data.get('thermal_zones', {}))}")
    print(f"🧠 QI: {thermal_memory.get_neural_system().get('qi_level', 'N/A')}")
    print(f"⚡ Neurones actifs: {thermal_memory.get_neural_system().get('active_neurons', 'N/A')}")
    print("✅ Serveur MCP prêt")
    
    # Test des fonctionnalités
    print("\n🧪 TEST DES FONCTIONNALITÉS:")
    
    # Test stats
    stats = thermal_memory.get_memory_stats()
    print(f"📊 Stats: {stats['total_zones']} zones, {stats['total_entries']} entrées")
    
    # Test zones chaudes
    hot_zones = thermal_memory.get_hot_zones(90.0)
    print(f"🔥 Zones chaudes (>90°C): {len(hot_zones)}")
    
    # Test recherche
    search_results = thermal_memory.search_memory("deepseek")
    print(f"🔍 Recherche 'deepseek': {len(search_results)} résultats")
    
    print("\n🎉 Serveur MCP opérationnel !")

if __name__ == "__main__":
    asyncio.run(main())
