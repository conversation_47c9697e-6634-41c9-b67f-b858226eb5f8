#!/usr/bin/env python3
"""
DeepSeek Memory Integration System
Direct memory access without simulation
Jean-Luc PASSAVE - 2025
"""

import json
import os
import sys
import threading
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import subprocess
import signal

class MemoryManager:
    def __init__(self, memory_file: str):
        self.memory_file = memory_file
        self.memory_data = None
        self.last_modified = 0
        self.lock = threading.Lock()
        
    def load_memory(self) -> bool:
        """Load thermal memory data"""
        try:
            with self.lock:
                stat = os.stat(self.memory_file)
                if stat.st_mtime > self.last_modified:
                    with open(self.memory_file, 'r', encoding='utf-8') as f:
                        self.memory_data = json.load(f)
                    self.last_modified = stat.st_mtime
                    return True
                return self.memory_data is not None
        except Exception as e:
            logging.error(f"Memory load error: {e}")
            return False
    
    def get_neural_data(self) -> Dict[str, Any]:
        """Get neural system data"""
        if not self.memory_data:
            return {}
        
        neural = self.memory_data.get('neural_system', {})
        return {
            'qi_level': neural.get('qi_level', 0),
            'active_neurons': neural.get('active_neurons', 0),
            'total_neurons': neural.get('total_neurons', 0),
            'neural_efficiency': neural.get('neural_efficiency', ''),
            'architecture_version': neural.get('architecture_version', '')
        }
    
    def get_thermal_zones(self, min_temp: float = 0) -> List[Dict[str, Any]]:
        """Get thermal zones above minimum temperature"""
        if not self.memory_data:
            return []
        
        zones = []
        thermal_zones = self.memory_data.get('thermal_zones', {})
        
        for zone_name, zone_data in thermal_zones.items():
            temp = zone_data.get('temperature', 0)
            if temp >= min_temp:
                zones.append({
                    'name': zone_name,
                    'temperature': temp,
                    'description': zone_data.get('description', ''),
                    'entries_count': len(zone_data.get('entries', [])),
                    'active': zone_data.get('active', True)
                })
        
        zones.sort(key=lambda x: x['temperature'], reverse=True)
        return zones
    
    def search_content(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search in memory content"""
        if not self.memory_data:
            return []
        
        results = []
        query_lower = query.lower()
        thermal_zones = self.memory_data.get('thermal_zones', {})
        
        for zone_name, zone_data in thermal_zones.items():
            # Search in zone description
            description = zone_data.get('description', '')
            if query_lower in description.lower():
                results.append({
                    'type': 'zone_description',
                    'zone': zone_name,
                    'temperature': zone_data.get('temperature', 0),
                    'content': description,
                    'score': 10
                })
            
            # Search in entries
            entries = zone_data.get('entries', [])
            for entry in entries:
                content = entry.get('content', '')
                if query_lower in content.lower():
                    results.append({
                        'type': 'entry',
                        'zone': zone_name,
                        'temperature': zone_data.get('temperature', 0),
                        'content': content,
                        'importance': entry.get('importance', 0),
                        'timestamp': entry.get('timestamp', 0),
                        'score': 5
                    })
        
        # Sort by score and temperature
        results.sort(key=lambda x: (x['score'], x['temperature']), reverse=True)
        return results[:limit]

class DeepSeekInterface:
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.server_url = "http://localhost:11434"
        
    def build_memory_context(self) -> str:
        """Build memory context for DeepSeek"""
        neural_data = self.memory_manager.get_neural_data()
        hot_zones = self.memory_manager.get_thermal_zones(min_temp=80)
        
        context = f"""NEURAL SYSTEM STATUS:
QI Level: {neural_data.get('qi_level', 'Unknown')}
Active Neurons: {neural_data.get('active_neurons', 'Unknown')}
Neural Efficiency: {neural_data.get('neural_efficiency', 'Unknown')}

THERMAL ZONES (≥80°C):"""
        
        for zone in hot_zones[:8]:
            context += f"""
{zone['name']}: {zone['temperature']}°C
Description: {zone['description']}
Entries: {zone['entries_count']}"""
        
        return context
    
    def query_with_memory(self, prompt: str) -> str:
        """Query DeepSeek with memory context"""
        if not self.memory_manager.load_memory():
            return "Memory access failed"
        
        memory_context = self.build_memory_context()
        
        full_prompt = f"""You are DeepSeek R1 8B with access to your thermal memory.

MEMORY CONTEXT:
{memory_context}

Use this memory data to answer the following question:
{prompt}

Response:"""
        
        try:
            import requests
            response = requests.post(
                f"{self.server_url}/api/generate",
                json={
                    "model": "deepseek-r1:8b-native",
                    "prompt": full_prompt,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('response', 'No response')
            else:
                return f"Server error: {response.status_code}"
                
        except Exception as e:
            return f"Connection error: {e}"

class SystemController:
    def __init__(self):
        self.memory_file = "./thermal_memory_real_clones_1749979850296.json"
        self.memory_manager = MemoryManager(self.memory_file)
        self.deepseek_interface = DeepSeekInterface(self.memory_manager)
        self.running = False
        
    def start_native_server(self):
        """Start native DeepSeek server"""
        try:
            # Kill existing Ollama processes
            subprocess.run(["pkill", "-f", "ollama"], check=False)
            
            # Start native server
            server_process = subprocess.Popen([
                sys.executable, "deepseek_native_server.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            time.sleep(2)  # Wait for server startup
            return server_process
            
        except Exception as e:
            logging.error(f"Server start error: {e}")
            return None
    
    def run_tests(self):
        """Run system tests"""
        print("Running system tests...")
        
        # Test memory loading
        if self.memory_manager.load_memory():
            print("✓ Memory loading: PASS")
        else:
            print("✗ Memory loading: FAIL")
            return False
        
        # Test neural data access
        neural_data = self.memory_manager.get_neural_data()
        if neural_data.get('qi_level'):
            print(f"✓ Neural data access: PASS (QI: {neural_data['qi_level']})")
        else:
            print("✗ Neural data access: FAIL")
            return False
        
        # Test thermal zones
        zones = self.memory_manager.get_thermal_zones(min_temp=80)
        if zones:
            print(f"✓ Thermal zones: PASS ({len(zones)} hot zones)")
        else:
            print("✗ Thermal zones: FAIL")
            return False
        
        # Test search
        results = self.memory_manager.search_content("deepseek")
        if results:
            print(f"✓ Memory search: PASS ({len(results)} results)")
        else:
            print("✗ Memory search: FAIL")
            return False
        
        return True
    
    def interactive_session(self):
        """Run interactive session"""
        print("\nDeepSeek Memory Integration System")
        print("Commands: /memory, /search <term>, /zones, /test, /quit")
        
        while True:
            try:
                user_input = input("\n> ")
                
                if user_input in ['/quit', '/exit']:
                    break
                
                elif user_input == '/memory':
                    context = self.deepseek_interface.build_memory_context()
                    print(f"\n{context}")
                
                elif user_input.startswith('/search '):
                    query = user_input[8:]
                    results = self.memory_manager.search_content(query)
                    print(f"\nSearch results for '{query}':")
                    for i, result in enumerate(results[:5], 1):
                        print(f"{i}. {result['zone']} ({result['temperature']}°C)")
                        print(f"   {result['content'][:100]}...")
                
                elif user_input == '/zones':
                    zones = self.memory_manager.get_thermal_zones(min_temp=70)
                    print(f"\nThermal zones (≥70°C):")
                    for zone in zones[:10]:
                        print(f"{zone['temperature']}°C - {zone['name']}")
                        print(f"  {zone['description']}")
                
                elif user_input == '/test':
                    self.run_tests()
                
                elif user_input.strip():
                    response = self.deepseek_interface.query_with_memory(user_input)
                    print(f"\nDeepSeek: {response}")
                
            except KeyboardInterrupt:
                break
        
        print("\nSession ended.")

def main():
    logging.basicConfig(level=logging.INFO)
    
    controller = SystemController()
    
    # Verify memory file exists
    if not os.path.exists(controller.memory_file):
        print(f"Error: Memory file not found: {controller.memory_file}")
        return 1
    
    # Run initial tests
    if not controller.run_tests():
        print("System tests failed")
        return 1
    
    # Start native server
    server_process = controller.start_native_server()
    if not server_process:
        print("Failed to start native server")
        return 1
    
    try:
        # Run interactive session
        controller.interactive_session()
    finally:
        # Cleanup
        if server_process:
            server_process.terminate()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
