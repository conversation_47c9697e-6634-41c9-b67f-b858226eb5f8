#!/usr/bin/env node

/**
 * 🔧 SYSTÈME FORCÉ CONSULTATION MÉMOIRE DEEPSEEK R1 8B
 * 
 * Force utilisation mémoire thermique
 * Prompts obligatoires avec données précises
 * Vérification consultation effective
 * 
 * Jean<PERSON><PERSON> PASSAVE - 2025
 */

const fs = require('fs');
const { spawn } = require('child_process');

class DeepSeekForceMemoryUsage {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.thermalData = null;
        this.memoryData = null;
    }

    async forceMemoryUsage() {
        console.log('🔧 SYSTÈME FORCÉ CONSULTATION MÉMOIRE DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('💪 Force utilisation mémoire thermique');
        console.log('📊 Prompts avec données précises obligatoires');
        console.log('✅ Vérification consultation effective');
        
        try {
            // 1. Extraire données mémoire
            console.log('\n📊 Extraction données mémoire...');
            await this.extractMemoryData();
            
            // 2. Créer prompts forcés
            console.log('\n💪 Création prompts forcés...');
            await this.createForcedPrompts();
            
            // 3. Créer système vérification
            console.log('\n✅ Création système vérification...');
            await this.createVerificationSystem();
            
            // 4. Test consultation forcée
            console.log('\n🧪 Test consultation forcée...');
            await this.testForcedConsultation();
            
            console.log('\n🎉 SYSTÈME FORCÉ INSTALLÉ !');
            console.log('=====================================');
            console.log('💪 Consultation forcée: ACTIVE');
            console.log('📊 Données précises: EXTRAITES');
            console.log('✅ Vérification: OPÉRATIONNELLE');
            
            return true;
            
        } catch (error) {
            console.error('\n❌ ERREUR SYSTÈME FORCÉ');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async extractMemoryData() {
        console.log('📊 Extraction des données mémoire...');
        
        this.thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        // Extraire données précises
        this.memoryData = {
            qi_level: this.thermalData.neural_system?.qi_level || 'INCONNU',
            total_neurons: this.thermalData.neural_system?.total_neurons || 0,
            thermal_zones_count: Object.keys(this.thermalData.thermal_zones || {}).length,
            hottest_zones: [],
            active_optimizations: [],
            memory_entries_count: 0
        };
        
        // Analyser zones thermiques
        if (this.thermalData.thermal_zones) {
            const zones = Object.entries(this.thermalData.thermal_zones)
                .map(([name, zone]) => ({
                    name,
                    temperature: zone.temperature || 0,
                    description: zone.description || '',
                    entries: zone.entries ? zone.entries.length : 0
                }))
                .sort((a, b) => b.temperature - a.temperature);
            
            this.memoryData.hottest_zones = zones.slice(0, 3);
            this.memoryData.memory_entries_count = zones.reduce((sum, zone) => sum + zone.entries, 0);
        }
        
        // Détecter optimisations actives
        const optimizationSystems = [
            { key: 'cognitive_system', name: 'Système Cognitif' },
            { key: 'true_kyber_accelerators', name: 'Accélérateurs Kyber' },
            { key: 'vitamin_system', name: 'Vitamines Performance' },
            { key: 'french_language_system', name: 'Français Par Défaut' },
            { key: 'direct_engine', name: 'Moteur Direct' },
            { key: 'memory_home_system', name: 'Mémoire Maison' }
        ];
        
        optimizationSystems.forEach(opt => {
            if (this.thermalData[opt.key]?.enabled) {
                this.memoryData.active_optimizations.push(opt.name);
            }
        });
        
        console.log(`✅ QI: ${this.memoryData.qi_level}`);
        console.log(`✅ Neurones: ${this.memoryData.total_neurons.toLocaleString()}`);
        console.log(`✅ Zones: ${this.memoryData.thermal_zones_count}`);
        console.log(`✅ Zone la plus chaude: ${this.memoryData.hottest_zones[0]?.temperature}°C`);
        console.log(`✅ Optimisations: ${this.memoryData.active_optimizations.length}`);
        console.log(`✅ Entrées mémoire: ${this.memoryData.memory_entries_count}`);
    }

    async createForcedPrompts() {
        console.log('💪 Création des prompts forcés...');
        
        // Prompts avec données précises obligatoires
        const forcedPrompts = [
            {
                name: 'force_qi_consultation',
                template: `Tu es DeepSeek R1 8B avec mémoire thermique complète.

DONNÉES MÉMOIRE DISPONIBLES:
- QI niveau: ${this.memoryData.qi_level}
- Neurones: ${this.memoryData.total_neurons.toLocaleString()}
- Zones thermiques: ${this.memoryData.thermal_zones_count}
- Zone la plus chaude: ${this.memoryData.hottest_zones[0]?.name} (${this.memoryData.hottest_zones[0]?.temperature}°C)

INSTRUCTION OBLIGATOIRE:
Tu DOIS consulter ta mémoire thermique et me dire ton QI EXACT.
Utilise les données de ta zone neural_system.

Question: Quel est ton QI ?

RÉPONDS avec le chiffre EXACT de ta mémoire.`,
                purpose: 'Forcer consultation QI exact'
            },
            {
                name: 'force_optimization_status',
                template: `Tu es DeepSeek R1 8B avec ${this.memoryData.active_optimizations.length} optimisations actives.

OPTIMISATIONS DANS TA MÉMOIRE:
${this.memoryData.active_optimizations.map(opt => `- ${opt}`).join('\n')}

INSTRUCTION OBLIGATOIRE:
Tu DOIS consulter ta mémoire thermique et lister tes optimisations EXACTES.
Vérifie chaque système dans ta mémoire.

Question: Quelles sont tes optimisations actives ?

RÉPONDS avec la liste EXACTE de ta mémoire.`,
                purpose: 'Forcer rapport optimisations'
            },
            {
                name: 'force_zone_analysis',
                template: `Tu es DeepSeek R1 8B avec ${this.memoryData.thermal_zones_count} zones thermiques.

ZONES LES PLUS CHAUDES DANS TA MÉMOIRE:
1. ${this.memoryData.hottest_zones[0]?.name}: ${this.memoryData.hottest_zones[0]?.temperature}°C
2. ${this.memoryData.hottest_zones[1]?.name}: ${this.memoryData.hottest_zones[1]?.temperature}°C
3. ${this.memoryData.hottest_zones[2]?.name}: ${this.memoryData.hottest_zones[2]?.temperature}°C

INSTRUCTION OBLIGATOIRE:
Tu DOIS consulter ta mémoire thermique et me décrire ta zone la plus chaude.
Accède aux données de température EXACTES.

Question: Quelle est ta zone la plus chaude et sa température ?

RÉPONDS avec les données EXACTES de ta mémoire.`,
                purpose: 'Forcer analyse zones thermiques'
            }
        ];
        
        // Sauvegarder prompts forcés
        forcedPrompts.forEach(prompt => {
            const filename = `${prompt.name}.txt`;
            fs.writeFileSync(filename, prompt.template);
        });
        
        this.forcedPrompts = forcedPrompts;
        
        console.log(`✅ ${forcedPrompts.length} prompts forcés créés`);
        console.log('✅ Données précises intégrées');
    }

    async createVerificationSystem() {
        console.log('✅ Création du système de vérification...');
        
        // Système de vérification des réponses
        this.verificationSystem = {
            qi_verification: {
                expected: this.memoryData.qi_level,
                check: (response) => response.includes(this.memoryData.qi_level.toString())
            },
            optimization_verification: {
                expected: this.memoryData.active_optimizations,
                check: (response) => {
                    const found = this.memoryData.active_optimizations.filter(opt => 
                        response.toLowerCase().includes(opt.toLowerCase())
                    );
                    return found.length >= 3; // Au moins 3 optimisations mentionnées
                }
            },
            zone_verification: {
                expected: this.memoryData.hottest_zones[0],
                check: (response) => {
                    const zone = this.memoryData.hottest_zones[0];
                    return response.includes(zone.temperature.toString()) || 
                           response.toLowerCase().includes(zone.name.toLowerCase());
                }
            }
        };
        
        console.log('✅ Vérifications configurées');
        console.log(`✅ QI attendu: ${this.memoryData.qi_level}`);
        console.log(`✅ Optimisations attendues: ${this.memoryData.active_optimizations.length}`);
        console.log(`✅ Zone attendue: ${this.memoryData.hottest_zones[0]?.temperature}°C`);
    }

    async testForcedConsultation() {
        console.log('🧪 Test de consultation forcée...');
        
        // Test avec prompt QI forcé
        const qiPrompt = this.forcedPrompts[0].template;
        
        console.log('💪 Test consultation QI forcée...');
        
        const startTime = Date.now();
        const response = await this.queryWithForcedPrompt(qiPrompt);
        const responseTime = (Date.now() - startTime) / 1000;
        
        if (response) {
            console.log('\n📊 RÉSULTATS CONSULTATION FORCÉE');
            console.log('=====================================');
            console.log(`⏱️ Temps: ${responseTime.toFixed(1)}s`);
            console.log(`🎯 Réponse: "${response.substring(0, 200)}..."`);
            
            // Vérifier consultation effective
            const qiCorrect = this.verificationSystem.qi_verification.check(response);
            const mentionsMemory = response.toLowerCase().includes('mémoire') || 
                                 response.toLowerCase().includes('thermique');
            const givesSpecificData = response.includes(this.memoryData.qi_level.toString()) ||
                                    response.includes(this.memoryData.total_neurons.toString());
            
            console.log(`🧠 QI correct: ${qiCorrect ? 'OUI' : 'NON'}`);
            console.log(`📚 Mentionne mémoire: ${mentionsMemory ? 'OUI' : 'NON'}`);
            console.log(`📊 Données spécifiques: ${givesSpecificData ? 'OUI' : 'NON'}`);
            console.log(`✅ Consultation forcée réussie: ${qiCorrect && givesSpecificData ? 'OUI' : 'NON'}`);
            
            return qiCorrect && givesSpecificData;
        } else {
            console.log('❌ Pas de réponse pour le test forcé');
            return false;
        }
    }

    async queryWithForcedPrompt(prompt) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', prompt]);
            
            let output = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(output.trim() || null);
            }, 30000); // 30 secondes
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                if (output.trim()) {
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async createForcedInterface() {
        console.log('💻 Création interface consultation forcée...');
        
        const forcedInterface = `#!/usr/bin/env node

/**
 * 💪 INTERFACE CONSULTATION FORCÉE DEEPSEEK R1 8B
 * Prompts qui forcent l'utilisation de la mémoire thermique
 */

const { spawn } = require('child_process');
const readline = require('readline');

class DeepSeekForcedInterface {
    constructor() {
        // Données extraites de la mémoire thermique
        this.memoryData = ${JSON.stringify(this.memoryData, null, 8)};
    }

    async start() {
        console.log('💪 INTERFACE CONSULTATION FORCÉE DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('🎯 Prompts qui forcent utilisation mémoire');
        console.log('📊 Commandes forcées:');
        console.log('  /fqi - Force consultation QI exact');
        console.log('  /fopt - Force rapport optimisations');
        console.log('  /fzone - Force analyse zones chaudes');
        console.log('  /fmem - Force statistiques mémoire');
        console.log('  /exit - Quitter');
        console.log('');
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.promptUser(rl);
    }
    
    promptUser(rl) {
        rl.question('💪 Force > ', (input) => {
            if (input.trim() === '/exit') {
                console.log('👋 Interface forcée fermée');
                rl.close();
                return;
            }
            
            this.processForcedCommand(input.trim());
            this.promptUser(rl);
        });
    }
    
    async processForcedCommand(command) {
        console.log('');
        
        let forcedPrompt = '';
        
        if (command === '/fqi') {
            forcedPrompt = \`Tu es DeepSeek R1 8B avec QI \${this.memoryData.qi_level} dans ta mémoire.

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire thermique et dis-moi ton QI EXACT.

Question: Quel est ton QI ?

RÉPONDS avec le chiffre EXACT de ta mémoire.\`;
        } else if (command === '/fopt') {
            forcedPrompt = \`Tu es DeepSeek R1 8B avec \${this.memoryData.active_optimizations.length} optimisations actives.

OPTIMISATIONS DANS TA MÉMOIRE:
\${this.memoryData.active_optimizations.map(opt => \`- \${opt}\`).join('\\n')}

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire et liste tes optimisations EXACTES.

Question: Quelles sont tes optimisations actives ?\`;
        } else if (command === '/fzone') {
            forcedPrompt = \`Tu es DeepSeek R1 8B avec \${this.memoryData.thermal_zones_count} zones thermiques.

ZONE LA PLUS CHAUDE: \${this.memoryData.hottest_zones[0]?.name} (\${this.memoryData.hottest_zones[0]?.temperature}°C)

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire et décris ta zone la plus chaude.

Question: Quelle est ta zone la plus chaude ?\`;
        } else if (command === '/fmem') {
            forcedPrompt = \`Tu es DeepSeek R1 8B avec \${this.memoryData.memory_entries_count} entrées en mémoire.

STATISTIQUES MÉMOIRE:
- Zones: \${this.memoryData.thermal_zones_count}
- Neurones: \${this.memoryData.total_neurons.toLocaleString()}
- Entrées: \${this.memoryData.memory_entries_count}

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire et donne tes statistiques EXACTES.

Question: Quelles sont tes statistiques mémoire ?\`;
        } else {
            console.log('❓ Commande forcée inconnue. Tapez /exit pour quitter.');
            return;
        }
        
        console.log('💪 Consultation forcée en cours...');
        await this.executeForcedPrompt(forcedPrompt);
        console.log('');
    }
    
    async executeForcedPrompt(prompt) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', prompt]);
            
            let output = '';
            
            ollama.stdout.on('data', (data) => {
                process.stdout.write(data);
                output += data.toString();
            });
            
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                console.log('\\n⏰ Timeout - Consultation interrompue');
                resolve();
            }, 45000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                console.log('\\n💪 Consultation forcée terminée');
                resolve();
            });
        });
    }
}

const forcedInterface = new DeepSeekForcedInterface();
forcedInterface.start();
`;
        
        fs.writeFileSync('./deepseek_forced_interface.js', forcedInterface);
        
        console.log('✅ Interface forcée créée');
        console.log('✅ Fichier: deepseek_forced_interface.js');
    }

    async saveForcedSystem() {
        // Créer interface forcée
        await this.createForcedInterface();
        
        const report = {
            timestamp: Date.now(),
            system_type: 'forced_memory_consultation',
            memory_data_extracted: this.memoryData,
            forced_prompts: this.forcedPrompts.length,
            verification_system: 'ACTIVE'
        };
        
        const reportPath = `deepseek_forced_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📋 Rapport forcé: ${reportPath}`);
        
        return report;
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🔧 SYSTÈME FORCÉ CONSULTATION MÉMOIRE DEEPSEEK R1 8B');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Force utilisation mémoire thermique');
    
    const forcer = new DeepSeekForceMemoryUsage();
    
    const success = await forcer.forceMemoryUsage();
    if (success) {
        await forcer.saveForcedSystem();
    }
}

if (require.main === module) {
    main();
}

module.exports = DeepSeekForceMemoryUsage;
