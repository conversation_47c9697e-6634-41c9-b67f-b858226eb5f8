#!/usr/bin/env node

/**
 * 🔥 DEEPSEEK R1 8B AVEC ACCÈS MÉMOIRE THERMIQUE
 * 
 * Interface qui permet à DeepSeek d'accéder à sa mémoire thermique
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');
const { spawn } = require('child_process');
const readline = require('readline');

class DeepSeekWithThermalMemory {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.thermalData = null;
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    async initialize() {
        console.log('🔥 DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE');
        console.log('=====================================');
        console.log('🧠 Chargement de la mémoire thermique...');
        
        try {
            this.thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            console.log('✅ Mémoire thermique chargée');
            console.log(`🧠 QI système: ${this.thermalData.neural_system?.qi_level || 'N/A'}`);
            console.log(`🧬 Neurones: ${(this.thermalData.neural_system?.total_neurons || 0).toLocaleString()}`);
            console.log(`🔥 Zones thermiques: ${Object.keys(this.thermalData.thermal_zones || {}).length}`);
            
            // Vérifier l'intégration DeepSeek
            const deepseekZone = this.thermalData.thermal_zones?.zone_deepseek_r1_authentic;
            if (deepseekZone) {
                console.log(`✅ Zone DeepSeek R1 8B trouvée (${deepseekZone.entries.length} entrées)`);
                console.log(`🌡️ Température: ${deepseekZone.temperature}°C`);
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur chargement mémoire thermique:', error.message);
            return false;
        }
    }

    async startInteractiveSession() {
        console.log('\n🎮 SESSION INTERACTIVE AVEC MÉMOIRE THERMIQUE');
        console.log('=====================================');
        console.log('💡 Tapez vos questions. DeepSeek aura accès à sa mémoire thermique.');
        console.log('💡 Commandes spéciales:');
        console.log('   /memory - Afficher l\'état de la mémoire');
        console.log('   /qi - Afficher le QI système');
        console.log('   /zones - Lister les zones thermiques');
        console.log('   /exit - Quitter');
        console.log('=====================================\n');
        
        this.promptUser();
    }

    promptUser() {
        this.rl.question('🔥 DeepSeek+Mémoire > ', async (input) => {
            const command = input.trim();
            
            if (command === '/exit') {
                console.log('👋 Au revoir Jean-Luc !');
                this.rl.close();
                return;
            }
            
            if (command === '/memory') {
                this.displayMemoryStatus();
                this.promptUser();
                return;
            }
            
            if (command === '/qi') {
                this.displayQIStatus();
                this.promptUser();
                return;
            }
            
            if (command === '/zones') {
                this.displayThermalZones();
                this.promptUser();
                return;
            }
            
            if (command.length === 0) {
                this.promptUser();
                return;
            }
            
            // Traiter la question avec contexte mémoire thermique
            await this.processQuestionWithMemory(command);
            this.promptUser();
        });
    }

    async processQuestionWithMemory(question) {
        console.log('\n🧠 Traitement avec mémoire thermique...');
        
        // Créer un contexte enrichi avec la mémoire thermique
        const memoryContext = this.buildMemoryContext();
        const enrichedPrompt = this.buildEnrichedPrompt(question, memoryContext);
        
        console.log('🤖 Consultation de DeepSeek R1 8B...');
        
        try {
            const response = await this.queryDeepSeekWithContext(enrichedPrompt);
            
            if (response) {
                console.log('\n📝 RÉPONSE DEEPSEEK R1 8B:');
                console.log('=====================================');
                console.log(response);
                console.log('=====================================');
                
                // Sauvegarder l'interaction dans la mémoire thermique
                await this.saveInteractionToMemory(question, response);
                
            } else {
                console.log('❌ Pas de réponse de DeepSeek R1 8B');
            }
            
        } catch (error) {
            console.error('❌ Erreur lors de la consultation:', error.message);
        }
    }

    buildMemoryContext() {
        const context = {
            qi_level: this.thermalData.neural_system?.qi_level || 0,
            total_neurons: this.thermalData.neural_system?.total_neurons || 0,
            deepseek_integration: this.thermalData.neural_system?.deepseek_r1_authentic_integration || {},
            zones_count: Object.keys(this.thermalData.thermal_zones || {}).length,
            recent_entries: []
        };
        
        // Récupérer les entrées récentes de toutes les zones
        Object.values(this.thermalData.thermal_zones || {}).forEach(zone => {
            if (zone.entries) {
                zone.entries.forEach(entry => {
                    if (entry.timestamp > (Date.now() / 1000) - 86400) { // Dernières 24h
                        context.recent_entries.push({
                            content: entry.content?.substring(0, 200) || '',
                            zone: entry.zone,
                            importance: entry.importance
                        });
                    }
                });
            }
        });
        
        return context;
    }

    buildEnrichedPrompt(question, memoryContext) {
        const prompt = `Tu es DeepSeek R1 8B intégré dans la mémoire thermique de Jean-Luc. Voici ton contexte actuel:

ÉTAT DE TA MÉMOIRE THERMIQUE:
- QI système: ${memoryContext.qi_level}
- Neurones totaux: ${memoryContext.total_neurons.toLocaleString()}
- Zones thermiques: ${memoryContext.zones_count}
- Intégration DeepSeek: ${memoryContext.deepseek_integration.active ? 'ACTIVE' : 'INACTIVE'}

INFORMATIONS RÉCENTES DANS TA MÉMOIRE:
${memoryContext.recent_entries.map(entry => `- ${entry.content}`).join('\n')}

QUESTION DE JEAN-LUC:
${question}

Réponds en tenant compte de ton intégration dans la mémoire thermique et de ton statut de DeepSeek R1 8B cloné authentiquement.`;

        return prompt;
    }

    async queryDeepSeekWithContext(prompt) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', prompt]);
            
            let output = '';
            let errorOutput = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            ollama.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            // Timeout après 90 secondes
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, 90000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                if (code === 0 && output.trim()) {
                    // Nettoyer la sortie
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async saveInteractionToMemory(question, response) {
        try {
            // Créer une zone pour les interactions si elle n'existe pas
            if (!this.thermalData.thermal_zones.zone_interactions_deepseek) {
                this.thermalData.thermal_zones.zone_interactions_deepseek = {
                    temperature: 37.2,
                    description: 'Interactions avec DeepSeek R1 8B',
                    entries: []
                };
            }
            
            // Ajouter l'interaction
            const interaction = {
                id: `interaction_${Date.now()}`,
                content: `INTERACTION DEEPSEEK - Question: "${question.substring(0, 100)}" - Réponse: "${response.substring(0, 200)}"`,
                importance: 0.8,
                timestamp: Math.floor(Date.now() / 1000),
                synaptic_strength: 0.9,
                temperature: 37.2,
                zone: 'zone_interactions_deepseek',
                source: 'deepseek_thermal_interface',
                type: 'interaction_log',
                full_question: question,
                full_response: response.substring(0, 1000)
            };
            
            this.thermalData.thermal_zones.zone_interactions_deepseek.entries.push(interaction);
            
            // Garder seulement les 50 dernières interactions
            if (this.thermalData.thermal_zones.zone_interactions_deepseek.entries.length > 50) {
                this.thermalData.thermal_zones.zone_interactions_deepseek.entries = 
                    this.thermalData.thermal_zones.zone_interactions_deepseek.entries.slice(-50);
            }
            
            // Sauvegarder
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(this.thermalData, null, 2));
            
            console.log('💾 Interaction sauvegardée dans la mémoire thermique');
            
        } catch (error) {
            console.log('⚠️ Erreur sauvegarde interaction:', error.message);
        }
    }

    displayMemoryStatus() {
        console.log('\n🧠 ÉTAT DE LA MÉMOIRE THERMIQUE');
        console.log('=====================================');
        console.log(`QI système: ${this.thermalData.neural_system?.qi_level || 'N/A'}`);
        console.log(`Neurones: ${(this.thermalData.neural_system?.total_neurons || 0).toLocaleString()}`);
        console.log(`Zones thermiques: ${Object.keys(this.thermalData.thermal_zones || {}).length}`);
        
        const deepseekIntegration = this.thermalData.neural_system?.deepseek_r1_authentic_integration;
        if (deepseekIntegration) {
            console.log(`DeepSeek intégré: ${deepseekIntegration.active ? 'OUI' : 'NON'}`);
            console.log(`Taille modèle: ${deepseekIntegration.model_size || 'N/A'}`);
            console.log(`Contribution QI: +${deepseekIntegration.qi_contribution || 0}`);
        }
    }

    displayQIStatus() {
        console.log('\n🧠 STATUT QI SYSTÈME');
        console.log('=====================================');
        const qiLevel = this.thermalData.neural_system?.qi_level || 0;
        const qiComponents = this.thermalData.neural_system?.qi_components || {};
        
        console.log(`QI Total: ${qiLevel}`);
        console.log('\nComposants QI:');
        Object.entries(qiComponents).forEach(([component, value]) => {
            console.log(`  - ${component}: +${value}`);
        });
    }

    displayThermalZones() {
        console.log('\n🔥 ZONES THERMIQUES');
        console.log('=====================================');
        Object.entries(this.thermalData.thermal_zones || {}).forEach(([zoneName, zone]) => {
            console.log(`${zoneName}:`);
            console.log(`  - Température: ${zone.temperature}°C`);
            console.log(`  - Entrées: ${zone.entries?.length || 0}`);
            console.log(`  - Description: ${zone.description || 'N/A'}`);
            console.log('');
        });
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🔥 DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Interface DeepSeek avec accès à la mémoire thermique');
    
    const deepseekInterface = new DeepSeekWithThermalMemory();
    
    const initialized = await deepseekInterface.initialize();
    if (initialized) {
        await deepseekInterface.startInteractiveSession();
    } else {
        console.log('❌ Impossible d\'initialiser l\'interface');
    }
}

if (require.main === module) {
    main();
}

module.exports = DeepSeekWithThermalMemory;
