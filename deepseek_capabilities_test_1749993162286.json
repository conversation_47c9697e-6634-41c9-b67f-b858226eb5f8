{"timestamp": 1749993162286, "test_type": "capabilities_test_post_training", "capabilities_tested": ["Formation IA 2024", "Fine-tuning avancé", "Prompt Engineering", "Cognitive Training", "Mode MCP + Internet", "Turbo kyber 10/10", "Régularisation", "Apprentissage adaptatif"], "successful_tests": 0, "total_tests": 5, "average_response_time": 30.003200000000003, "speed_improvement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quality_improvement": "Faible", "training_effective": false, "overall_score": 0, "detailed_results": [{"test": "Vitesse réflexion", "question": "15 × 24", "response_time": 30.002, "target_time": 10, "correct": null, "fast_enough": false, "success": null}, {"test": "Qualité cognitive", "question": "Photosynthèse", "response_time": 30.003, "has_key_terms": null, "well_structured": null, "success": null}, {"test": "Prompt Engineering", "question": "Solutions énergie", "response_time": 30.004, "has_structure": null, "is_creative": null, "mentions_training": null, "success": null}, {"test": "Fine-tuning effects", "question": "<PERSON><PERSON><PERSON>", "response_time": 30.004, "correct_logic": null, "shows_reasoning": null, "success": null}, {"test": "Capacités avancées", "question": "IA et environnement", "response_time": 30.003, "word_count": 0, "is_environmental": null, "is_concise": false, "success": null}]}