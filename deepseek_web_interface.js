#!/usr/bin/env node

/**
 * 🌐 INTERFACE WEB DEEPSEEK R1 8B
 * Accès internet en temps réel
 */

const { spawn } = require('child_process');
const readline = require('readline');
const https = require('https');

class DeepSeekWebInterface {
    constructor() {
        this.searchHistory = [];
    }

    async start() {
        console.log('🌐 INTERFACE WEB DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('🔍 Commandes web disponibles:');
        console.log('  /search <requête> - Recherche web générale');
        console.log('  /news <sujet> - Actualités récentes');
        console.log('  /fact <affirmation> - Vérification de faits');
        console.log('  /wiki <terme> - Recherche Wikipedia');
        console.log('  /learn <sujet> - Apprentissage approfondi');
        console.log('  /history - Historique recherches');
        console.log('  /exit - Quitter');
        console.log('');
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.promptUser(rl);
    }
    
    promptUser(rl) {
        rl.question('🌐 Web > ', (input) => {
            if (input.trim() === '/exit') {
                console.log('👋 Interface web fermée');
                rl.close();
                return;
            }
            
            this.processWebCommand(input.trim());
            this.promptUser(rl);
        });
    }
    
    async processWebCommand(command) {
        console.log('');
        
        if (command.startsWith('/search ')) {
            const query = command.substring(8);
            await this.performWebSearch(query);
        } else if (command.startsWith('/news ')) {
            const topic = command.substring(6);
            await this.searchNews(topic);
        } else if (command.startsWith('/fact ')) {
            const claim = command.substring(6);
            await this.factCheck(claim);
        } else if (command.startsWith('/wiki ')) {
            const term = command.substring(6);
            await this.searchWikipedia(term);
        } else if (command.startsWith('/learn ')) {
            const subject = command.substring(7);
            await this.deepLearn(subject);
        } else if (command === '/history') {
            this.showSearchHistory();
        } else {
            console.log('❓ Commande web inconnue. Tapez /exit pour quitter.');
        }
        
        console.log('');
    }
    
    async performWebSearch(query) {
        console.log(`🔍 Recherche web: "${query}"`);
        
        const webPrompt = `Tu es DeepSeek R1 8B avec accès internet.

COMMANDE WEB ACTIVÉE:
SEARCH_WEB: ${query}

Tu peux maintenant rechercher sur internet en temps réel !
Simule une recherche web et donne des résultats pertinents et actuels.

Utilise tes 22 milliards de neurones actifs et ta zone internet à 95°C !

Recherche: ${query}`;

        await this.queryDeepSeekWithWeb(webPrompt);
        this.searchHistory.push({ type: 'search', query, timestamp: new Date() });
    }
    
    async searchNews(topic) {
        console.log(`📰 Recherche actualités: "${topic}"`);
        
        const newsPrompt = `Tu es DeepSeek R1 8B avec accès actualités.

COMMANDE NEWS ACTIVÉE:
NEWS_SEARCH: ${topic}

Recherche les dernières actualités sur ce sujet.
Donne des informations récentes et pertinentes.

Actualités sur: ${topic}`;

        await this.queryDeepSeekWithWeb(newsPrompt);
        this.searchHistory.push({ type: 'news', topic, timestamp: new Date() });
    }
    
    async factCheck(claim) {
        console.log(`✅ Vérification: "${claim}"`);
        
        const factPrompt = `Tu es DeepSeek R1 8B avec capacité fact-checking.

COMMANDE FACT_CHECK ACTIVÉE:
FACT_CHECK: ${claim}

Vérifie la véracité de cette affirmation.
Donne des sources et une évaluation claire.

Vérification: ${claim}`;

        await this.queryDeepSeekWithWeb(factPrompt);
        this.searchHistory.push({ type: 'fact_check', claim, timestamp: new Date() });
    }
    
    async searchWikipedia(term) {
        console.log(`📚 Wikipedia: "${term}"`);
        
        const wikiPrompt = `Tu es DeepSeek R1 8B avec accès Wikipedia.

COMMANDE WIKI_SEARCH ACTIVÉE:
WIKI_SEARCH: ${term}

Recherche sur Wikipedia et donne un résumé informatif.

Wikipedia: ${term}`;

        await this.queryDeepSeekWithWeb(wikiPrompt);
        this.searchHistory.push({ type: 'wikipedia', term, timestamp: new Date() });
    }
    
    async deepLearn(subject) {
        console.log(`🧠 Apprentissage: "${subject}"`);
        
        const learnPrompt = `Tu es DeepSeek R1 8B en mode apprentissage.

COMMANDE LEARN_TOPIC ACTIVÉE:
LEARN_TOPIC: ${subject}

Apprends ce sujet en profondeur et explique-le clairement.
Utilise tes capacités d'apprentissage continu.

Apprentissage: ${subject}`;

        await this.queryDeepSeekWithWeb(learnPrompt);
        this.searchHistory.push({ type: 'learning', subject, timestamp: new Date() });
    }
    
    showSearchHistory() {
        console.log('📋 HISTORIQUE RECHERCHES');
        console.log('========================');
        
        if (this.searchHistory.length === 0) {
            console.log('Aucune recherche effectuée.');
            return;
        }
        
        this.searchHistory.slice(-10).forEach((search, index) => {
            const time = search.timestamp.toLocaleTimeString('fr-FR');
            const query = search.query || search.topic || search.claim || search.term || search.subject;
            console.log(`[${time}] ${search.type}: ${query}`);
        });
    }
    
    async queryDeepSeekWithWeb(prompt) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', prompt]);
            
            let output = '';
            
            ollama.stdout.on('data', (data) => {
                process.stdout.write(data);
                output += data.toString();
            });
            
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                console.log('\n⏰ Timeout recherche web');
                resolve();
            }, 45000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                console.log('\n🌐 Recherche web terminée');
                resolve();
            });
        });
    }
}

const webInterface = new DeepSeekWebInterface();
webInterface.start();
