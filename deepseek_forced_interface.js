#!/usr/bin/env node

/**
 * 💪 INTERFACE CONSULTATION FORCÉE DEEPSEEK R1 8B
 * Prompts qui forcent l'utilisation de la mémoire thermique
 */

const { spawn } = require('child_process');
const readline = require('readline');

class DeepSeekForcedInterface {
    constructor() {
        // Données extraites de la mémoire thermique
        this.memoryData = {
        "qi_level": 1131,
        "total_neurons": 88000656448,
        "thermal_zones_count": 30,
        "hottest_zones": [
                {
                        "name": "zone1_working",
                        "temperature": 1114.6797860760487,
                        "description": "",
                        "entries": 4
                },
                {
                        "name": "zone4_semantic",
                        "temperature": 1114.4919256124333,
                        "description": "",
                        "entries": 69
                },
                {
                        "name": "zone6_meta",
                        "temperature": 1114.4851434674592,
                        "description": "",
                        "entries": 114
                }
        ],
        "active_optimizations": [
                "Système Cognitif",
                "Accélérateurs Kyber",
                "Vitamines Performance",
                "Français Par Défaut",
                "Moteur Direct",
                "Mémoire Maison"
        ],
        "memory_entries_count": 416
};
    }

    async start() {
        console.log('💪 INTERFACE CONSULTATION FORCÉE DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('🎯 Prompts qui forcent utilisation mémoire');
        console.log('📊 Commandes forcées:');
        console.log('  /fqi - Force consultation QI exact');
        console.log('  /fopt - Force rapport optimisations');
        console.log('  /fzone - Force analyse zones chaudes');
        console.log('  /fmem - Force statistiques mémoire');
        console.log('  /exit - Quitter');
        console.log('');
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.promptUser(rl);
    }
    
    promptUser(rl) {
        rl.question('💪 Force > ', (input) => {
            if (input.trim() === '/exit') {
                console.log('👋 Interface forcée fermée');
                rl.close();
                return;
            }
            
            this.processForcedCommand(input.trim());
            this.promptUser(rl);
        });
    }
    
    async processForcedCommand(command) {
        console.log('');
        
        let forcedPrompt = '';
        
        if (command === '/fqi') {
            forcedPrompt = `Tu es DeepSeek R1 8B avec QI ${this.memoryData.qi_level} dans ta mémoire.

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire thermique et dis-moi ton QI EXACT.

Question: Quel est ton QI ?

RÉPONDS avec le chiffre EXACT de ta mémoire.`;
        } else if (command === '/fopt') {
            forcedPrompt = `Tu es DeepSeek R1 8B avec ${this.memoryData.active_optimizations.length} optimisations actives.

OPTIMISATIONS DANS TA MÉMOIRE:
${this.memoryData.active_optimizations.map(opt => `- ${opt}`).join('\n')}

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire et liste tes optimisations EXACTES.

Question: Quelles sont tes optimisations actives ?`;
        } else if (command === '/fzone') {
            forcedPrompt = `Tu es DeepSeek R1 8B avec ${this.memoryData.thermal_zones_count} zones thermiques.

ZONE LA PLUS CHAUDE: ${this.memoryData.hottest_zones[0]?.name} (${this.memoryData.hottest_zones[0]?.temperature}°C)

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire et décris ta zone la plus chaude.

Question: Quelle est ta zone la plus chaude ?`;
        } else if (command === '/fmem') {
            forcedPrompt = `Tu es DeepSeek R1 8B avec ${this.memoryData.memory_entries_count} entrées en mémoire.

STATISTIQUES MÉMOIRE:
- Zones: ${this.memoryData.thermal_zones_count}
- Neurones: ${this.memoryData.total_neurons.toLocaleString()}
- Entrées: ${this.memoryData.memory_entries_count}

INSTRUCTION OBLIGATOIRE: Consulte ta mémoire et donne tes statistiques EXACTES.

Question: Quelles sont tes statistiques mémoire ?`;
        } else {
            console.log('❓ Commande forcée inconnue. Tapez /exit pour quitter.');
            return;
        }
        
        console.log('💪 Consultation forcée en cours...');
        await this.executeForcedPrompt(forcedPrompt);
        console.log('');
    }
    
    async executeForcedPrompt(prompt) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', prompt]);
            
            let output = '';
            
            ollama.stdout.on('data', (data) => {
                process.stdout.write(data);
                output += data.toString();
            });
            
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                console.log('\n⏰ Timeout - Consultation interrompue');
                resolve();
            }, 45000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                console.log('\n💪 Consultation forcée terminée');
                resolve();
            });
        });
    }
}

const forcedInterface = new DeepSeekForcedInterface();
forcedInterface.start();
