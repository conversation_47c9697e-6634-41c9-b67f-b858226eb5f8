{"timestamp": 1749985587785, "total_tests": 5, "passed_tests": 1, "failed_tests": 4, "success_rate": 20, "total_duration": 180.12800000000001, "is_authentic": false, "test_details": [{"test": "Calcul simple", "correct": false, "duration": 45.003, "error": "No response"}, {"test": "Génération de code", "correct": false, "duration": 45.004, "error": "No response"}, {"test": "Raisonnement logique", "correct": false, "duration": 45.013, "error": "No response"}, {"test": "Conversation", "correct": false, "duration": 45.008, "error": "No response"}, {"test": "Intégration mémoire thermique", "correct": true, "duration": 0.1, "details": {"hasDeepSeekZone": {"temperature": 38.5, "description": "Zone DeepSeek R1 8B Authentique Cloné (cp -r)", "entries": [{"id": "deepseek_r1_authentic_1749985260914", "content": "DEEPSEEK R1 8B AUTHENTIQUE CLONÉ - Modèle 8B paramètres extrait avec méthode cp -r. Clone 100% identique de 19.14GB. Intégration dans mémoire thermique réelle de Jean-Luc. Capacités: raisonnement avancé, génération de code, pensée mathématique.", "importance": 1, "timestamp": 1749985260, "synaptic_strength": 1, "temperature": 38.5, "zone": "zone_deepseek_r1_authentic", "source": "authentic_deepseek_cloner", "type": "authentic_cloned_model", "clone_data": {"clone_id": "deepseek_r1_authentic_1749984237489", "model_name": "DeepSeek R1 8B", "clone_path": "cloned_agents/deepseek_r1_authentic_1749984237489", "size_gb": 19.14, "file_count": 38795, "clone_method": "cp_recursive", "authenticity": "verified_100_percent", "integration_timestamp": 1749985260915, "capabilities": {"reasoning": 0.95, "code_generation": 0.92, "mathematical_thinking": 0.88, "conversation": 0.9, "parameters": "8B"}}}], "creation_timestamp": 1749985260914, "clone_method": "cp_recursive", "authenticity": "verified_authentic"}, "currentQI": 711.0391299999999, "hasQIBoost": true, "hasDeepSeekIntegration": true}}]}