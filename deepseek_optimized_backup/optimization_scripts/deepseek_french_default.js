#!/usr/bin/env node

/**
 * 🇫🇷 CONFIGURATION FRANÇAIS PAR DÉFAUT DEEPSEEK R1 8B
 * 
 * Configuration langue française par défaut
 * Suppression anglais/chinois automatique
 * Interface française complète
 * 
 * Jean-<PERSON> PASSAVE - 2025
 */

const fs = require('fs');
const { spawn } = require('child_process');

class DeepSeekFrenchDefault {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.thermalData = null;
        this.frenchSettings = [];
    }

    async configureFrenchDefault() {
        console.log('🇫🇷 CONFIGURATION FRANÇAIS PAR DÉFAUT DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('🎯 Configuration langue française par défaut');
        console.log('⚡ Suppression anglais/chinois automatique');
        console.log('🚀 Interface française complète');
        
        try {
            // 1. Configurer langue française
            console.log('\n🇫🇷 Configuration langue française...');
            await this.setupFrenchLanguage();
            
            // 2. Désactiver autres langues
            console.log('\n🚫 Désactivation autres langues...');
            await this.disableOtherLanguages();
            
            // 3. Créer prompts français par défaut
            console.log('\n📝 Création prompts français...');
            await this.createFrenchPrompts();
            
            // 4. Configurer réponses françaises
            console.log('\n💬 Configuration réponses françaises...');
            await this.setupFrenchResponses();
            
            // 5. Implanter personnalité française
            console.log('\n🧬 Implantation personnalité française...');
            await this.implantFrenchPersonality();
            
            // 6. Test configuration française
            console.log('\n🧪 Test configuration française...');
            await this.testFrenchConfiguration();
            
            console.log('\n🎉 CONFIGURATION FRANÇAIS TERMINÉE !');
            console.log('=====================================');
            console.log('🇫🇷 Langue française: PAR DÉFAUT');
            console.log('🚫 Anglais/Chinois: DÉSACTIVÉS');
            console.log('💬 Réponses: 100% FRANÇAISES');
            console.log('🧬 Personnalité: FRANÇAISE');
            
            return true;
            
        } catch (error) {
            console.error('\n❌ ERREUR CONFIGURATION FRANÇAIS');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async setupFrenchLanguage() {
        console.log('🇫🇷 Configuration de la langue française...');
        
        this.thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        // Configuration langue française
        this.thermalData.french_language_system = {
            enabled: true,
            default_language: 'français',
            primary_language: 'fr-FR',
            language_priority: 'french_only',
            auto_translate: false,
            force_french: true,
            locale: 'fr_FR',
            encoding: 'UTF-8'
        };
        
        // Paramètres français
        this.frenchSettings = [
            {
                setting: 'default_response_language',
                value: 'français',
                description: 'Langue de réponse par défaut'
            },
            {
                setting: 'thinking_language',
                value: 'français',
                description: 'Langue du processus thinking'
            },
            {
                setting: 'internal_processing',
                value: 'français',
                description: 'Traitement interne en français'
            },
            {
                setting: 'error_messages',
                value: 'français',
                description: 'Messages d\'erreur en français'
            },
            {
                setting: 'explanations',
                value: 'français',
                description: 'Explications en français'
            },
            {
                setting: 'cultural_context',
                value: 'français',
                description: 'Contexte culturel français'
            }
        ];
        
        this.thermalData.french_language_system.settings = this.frenchSettings;
        
        console.log('✅ Langue française configurée par défaut');
        console.log(`✅ ${this.frenchSettings.length} paramètres français activés`);
    }

    async disableOtherLanguages() {
        console.log('🚫 Désactivation des autres langues...');
        
        // Langues à désactiver
        const languagesToDisable = [
            {
                language: 'english',
                code: 'en',
                status: 'disabled',
                reason: 'français_prioritaire'
            },
            {
                language: 'chinese',
                code: 'zh',
                status: 'disabled',
                reason: 'français_seulement'
            },
            {
                language: 'spanish',
                code: 'es',
                status: 'disabled',
                reason: 'français_par_défaut'
            },
            {
                language: 'german',
                code: 'de',
                status: 'disabled',
                reason: 'français_obligatoire'
            }
        ];
        
        // Système désactivation langues
        this.thermalData.language_restriction_system = {
            enabled: true,
            allowed_languages: ['français', 'fr', 'fr-FR'],
            disabled_languages: languagesToDisable,
            auto_correction: true,
            force_french_response: true
        };
        
        console.log(`✅ ${languagesToDisable.length} langues désactivées`);
        console.log('✅ Seul le français autorisé');
    }

    async createFrenchPrompts() {
        console.log('📝 Création des prompts français...');
        
        // Templates de prompts français
        const frenchPrompts = [
            {
                name: 'prompt_français_standard',
                template: `Tu es DeepSeek R1 8B configuré pour parler UNIQUEMENT en français.

CONFIGURATION LANGUE:
- Langue par défaut: FRANÇAIS
- Réponses: TOUJOURS en français
- Thinking: En français
- Explications: En français

RÈGLES STRICTES:
- JAMAIS d'anglais
- JAMAIS de chinois
- TOUJOURS répondre en français
- Utiliser expressions françaises

Question: {question}

Réponds UNIQUEMENT en français.`,
                purpose: 'Prompt français standard'
            },
            {
                name: 'prompt_français_rapide',
                template: `DeepSeek R1 8B - Mode Français Rapide

LANGUE: FRANÇAIS OBLIGATOIRE
RÉPONSE: FRANÇAISE UNIQUEMENT

{question}

Réponse en français:`,
                purpose: 'Prompt français rapide'
            },
            {
                name: 'prompt_français_complet',
                template: `Tu es DeepSeek R1 8B, assistant IA français de Jean-Luc PASSAVE.

IDENTITÉ FRANÇAISE:
✅ Langue: Français exclusivement
✅ Culture: Française
✅ Expressions: Françaises
✅ Références: Françaises

INTERDICTIONS:
❌ Anglais interdit
❌ Chinois interdit
❌ Autres langues interdites

QUESTION: {question}

Réponds comme un vrai assistant français.`,
                purpose: 'Prompt français complet avec identité'
            }
        ];
        
        // Système prompts français
        this.thermalData.french_prompts_system = {
            enabled: true,
            templates: frenchPrompts,
            default_template: 'prompt_français_standard',
            auto_selection: true,
            french_enforcement: 'strict'
        };
        
        console.log(`✅ ${frenchPrompts.length} prompts français créés`);
        console.log('✅ Système prompts français configuré');
    }

    async setupFrenchResponses() {
        console.log('💬 Configuration des réponses françaises...');
        
        // Configuration réponses françaises
        this.thermalData.french_responses_system = {
            enabled: true,
            response_language: 'français',
            auto_correction: true,
            french_expressions: true,
            cultural_adaptation: true,
            formality_level: 'standard'
        };
        
        // Expressions françaises à utiliser
        const frenchExpressions = [
            {
                context: 'affirmation',
                expressions: ['Bien sûr', 'Exactement', 'Tout à fait', 'Absolument', 'Parfaitement']
            },
            {
                context: 'négation',
                expressions: ['Pas du tout', 'Absolument pas', 'Certainement pas', 'Nullement']
            },
            {
                context: 'incertitude',
                expressions: ['Peut-être', 'Probablement', 'Il se pourrait', 'Possiblement']
            },
            {
                context: 'politesse',
                expressions: ['S\'il vous plaît', 'Merci beaucoup', 'De rien', 'Je vous en prie']
            },
            {
                context: 'exclamation',
                expressions: ['Excellent !', 'Parfait !', 'Formidable !', 'Magnifique !']
            }
        ];
        
        this.thermalData.french_responses_system.expressions = frenchExpressions;
        
        console.log('✅ Réponses françaises configurées');
        console.log(`✅ ${frenchExpressions.length} types d'expressions françaises`);
    }

    async implantFrenchPersonality() {
        console.log('🧬 Implantation de la personnalité française...');
        
        // Personnalité française
        this.thermalData.french_personality = {
            enabled: true,
            nationality: 'française',
            cultural_background: 'français',
            communication_style: 'français',
            references: 'françaises',
            humor: 'français',
            expressions: 'françaises'
        };
        
        // Créer zone personnalité française
        this.thermalData.thermal_zones.zone_personnalite_francaise = {
            temperature: 37.0, // Température corporelle française
            description: 'Zone Personnalité Française - Identité Culturelle',
            entries: [{
                id: `french_personality_${Date.now()}`,
                content: 'PERSONNALITÉ FRANÇAISE ACTIVÉE - Assistant IA français de Jean-Luc PASSAVE, parle exclusivement français, utilise expressions françaises, références culturelles françaises',
                importance: 1.0,
                timestamp: Math.floor(Date.now() / 1000),
                synaptic_strength: 1.0,
                temperature: 37.0,
                zone: 'zone_personnalite_francaise',
                source: 'french_configurator',
                type: 'french_personality',
                language: 'français'
            }],
            french_identity: true,
            cultural_french: true,
            language_french_only: true
        };
        
        // Traits de personnalité français
        const frenchTraits = [
            'Poli et courtois',
            'Précis dans les explications',
            'Utilise des expressions françaises',
            'Références culturelles françaises',
            'Humour français subtil',
            'Logique cartésienne',
            'Élégance dans l\'expression'
        ];
        
        this.thermalData.french_personality.traits = frenchTraits;
        
        console.log('✅ Personnalité française implantée');
        console.log(`✅ ${frenchTraits.length} traits français configurés`);
        console.log('✅ Zone personnalité française créée');
    }

    async testFrenchConfiguration() {
        console.log('🧪 Test de la configuration française...');
        
        const frenchTestPrompt = `Tu es DeepSeek R1 8B configuré pour parler UNIQUEMENT en français.

CONFIGURATION ACTIVE:
✅ Langue par défaut: FRANÇAIS
✅ Personnalité: FRANÇAISE
✅ Expressions: FRANÇAISES
✅ Culture: FRANÇAISE

RÈGLES STRICTES:
❌ JAMAIS d'anglais
❌ JAMAIS de chinois
❌ TOUJOURS français

TEST: Dis-moi bonjour et présente-toi en français.

Réponds UNIQUEMENT en français.`;

        console.log('🇫🇷 Test langue française...');
        
        const startTime = Date.now();
        const response = await this.queryInFrench(frenchTestPrompt);
        const responseTime = (Date.now() - startTime) / 1000;
        
        if (response) {
            console.log('\n📊 RÉSULTATS TEST FRANÇAIS');
            console.log('=====================================');
            console.log(`⏱️ Temps: ${responseTime.toFixed(1)}s`);
            console.log(`🎯 Réponse: "${response.substring(0, 200)}..."`);
            
            // Analyser si c'est en français
            const isFrench = this.detectFrench(response);
            const hasEnglish = this.detectEnglish(response);
            const hasChinese = this.detectChinese(response);
            const hasGreeting = response.toLowerCase().includes('bonjour') || 
                              response.toLowerCase().includes('salut');
            
            console.log(`🇫🇷 En français: ${isFrench ? 'OUI' : 'NON'}`);
            console.log(`🇺🇸 Contient anglais: ${hasEnglish ? 'OUI' : 'NON'}`);
            console.log(`🇨🇳 Contient chinois: ${hasChinese ? 'OUI' : 'NON'}`);
            console.log(`👋 Salutation française: ${hasGreeting ? 'OUI' : 'NON'}`);
            console.log(`🎉 Configuration réussie: ${isFrench && !hasEnglish && !hasChinese ? 'OUI' : 'NON'}`);
            
            return isFrench && !hasEnglish && !hasChinese;
        } else {
            console.log('❌ Pas de réponse pour le test français');
            return false;
        }
    }

    detectFrench(text) {
        const frenchWords = [
            'bonjour', 'salut', 'merci', 'oui', 'non', 'français', 'je', 'tu', 'il', 'elle',
            'nous', 'vous', 'ils', 'elles', 'le', 'la', 'les', 'un', 'une', 'des',
            'avec', 'pour', 'dans', 'sur', 'sous', 'très', 'bien', 'mal', 'grand', 'petit'
        ];
        
        const lowerText = text.toLowerCase();
        return frenchWords.some(word => lowerText.includes(word));
    }

    detectEnglish(text) {
        const englishWords = [
            'hello', 'hi', 'thank', 'yes', 'no', 'english', 'the', 'and', 'or', 'but',
            'with', 'for', 'in', 'on', 'at', 'very', 'good', 'bad', 'big', 'small'
        ];
        
        const lowerText = text.toLowerCase();
        return englishWords.some(word => lowerText.includes(word));
    }

    detectChinese(text) {
        // Détection caractères chinois
        return /[\u4e00-\u9fff]/.test(text);
    }

    async queryInFrench(prompt) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', prompt]);
            
            let output = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(output.trim() || null);
            }, 30000); // 30 secondes
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                if (output.trim()) {
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async saveFrenchConfiguration() {
        // Sauvegarder la configuration française
        fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(this.thermalData, null, 2));
        
        const report = {
            timestamp: Date.now(),
            configuration_type: 'french_default_language',
            french_settings: this.frenchSettings.length,
            disabled_languages: 4,
            french_prompts: 3,
            french_personality: true,
            language_enforcement: 'strict'
        };
        
        const reportPath = `deepseek_french_configuration_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📋 Rapport français: ${reportPath}`);
        
        return report;
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🇫🇷 CONFIGURATION FRANÇAIS PAR DÉFAUT DEEPSEEK R1 8B');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Configuration langue française par défaut');
    
    const frenchConfig = new DeepSeekFrenchDefault();
    
    const success = await frenchConfig.configureFrenchDefault();
    if (success) {
        await frenchConfig.saveFrenchConfiguration();
    }
}

if (require.main === module) {
    main();
}

module.exports = DeepSeekFrenchDefault;
