#!/bin/bash

# 🔄 SCRIPT RESTAURATION DEEPSEEK R1 8B OPTIMISÉ
# Jean<PERSON><PERSON> PASSAVE - 2025

echo "🔄 RESTAURATION DEEPSEEK R1 8B OPTIMISÉ"
echo "====================================="

# Restaurer mémoire thermique
if [ -f "thermal_memory/thermal_memory_optimized.json" ]; then
    cp thermal_memory/thermal_memory_optimized.json ../thermal_memory_real_clones_1749979850296.json
    echo "✅ Mémoire thermique restaurée"
fi

# Restaurer scripts
if [ -d "optimization_scripts" ]; then
    cp optimization_scripts/* ../
    echo "✅ Scripts d'optimisation restaurés"
fi

# Restaurer interface
if [ -f "interfaces/deepseek_direct_interface.js" ]; then
    cp interfaces/deepseek_direct_interface.js ../deepseek_alternative_interface.js
    echo "✅ Interface directe restaurée"
fi

echo ""
echo "🎉 RESTAURATION TERMINÉE !"
echo "Votre DeepSeek R1 8B optimisé est prêt !"
echo ""
echo "Démarrage interface directe:"
echo "node ../deepseek_alternative_interface.js"
