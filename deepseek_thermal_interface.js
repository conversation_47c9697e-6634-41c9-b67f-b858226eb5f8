#!/usr/bin/env node

/**
 * 🔥 INTERFACE DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE
 * 
 * Interface complète avec votre DeepSeek R1 8B intégré
 * Connexion directe à la mémoire thermique
 * Remplacement de l'ancien agent
 * 
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');
const { spawn } = require('child_process');
const readline = require('readline');

class DeepSeekThermalInterface {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.thermalData = null;
        this.agentName = 'DeepSeek R1 8B Authentique';
        this.agentPersonality = 'Assistant IA avancé avec mémoire thermique';
        this.conversationHistory = [];
        this.rl = null;
    }

    async initialize() {
        console.log('🔥 INTERFACE DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE');
        console.log('=====================================');
        console.log('🤖 Votre agent DeepSeek R1 8B authentique');
        console.log('🧠 Connexion directe à la mémoire thermique');
        
        try {
            // Charger la mémoire thermique
            console.log('\n🧠 Chargement mémoire thermique...');
            await this.loadThermalMemory();
            
            // Configurer l'interface
            console.log('\n🎮 Configuration interface...');
            this.setupInterface();
            
            // Vérifier l'intégration DeepSeek
            console.log('\n🔍 Vérification intégration DeepSeek...');
            const integration = this.checkDeepSeekIntegration();
            
            console.log('\n✅ INTERFACE PRÊTE !');
            console.log('=====================================');
            console.log(`🤖 Agent: ${this.agentName}`);
            console.log(`🧠 QI système: ${this.thermalData.neural_system?.qi_level || 'N/A'}`);
            console.log(`🔥 Zones thermiques: ${Object.keys(this.thermalData.thermal_zones || {}).length}`);
            console.log(`⚡ Kyber niveau: ${this.thermalData.living_kyber_system?.base_level || 'N/A'}/10`);
            console.log(`🧬 Évolutions: ${this.thermalData.evolution_history?.length || 0}`);
            
            return true;
            
        } catch (error) {
            console.error('\n❌ ERREUR INITIALISATION INTERFACE');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async loadThermalMemory() {
        if (!fs.existsSync(this.thermalMemoryPath)) {
            throw new Error('Fichier mémoire thermique non trouvé');
        }
        
        this.thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
        
        console.log('✅ Mémoire thermique chargée');
        console.log(`🧠 QI: ${this.thermalData.neural_system?.qi_level || 'N/A'}`);
        console.log(`🧬 Neurones: ${(this.thermalData.neural_system?.total_neurons || 0).toLocaleString()}`);
    }

    setupInterface() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        console.log('✅ Interface configurée');
    }

    checkDeepSeekIntegration() {
        const deepseekZone = this.thermalData.thermal_zones?.zone_deepseek_r1_authentic;
        const integration = this.thermalData.neural_system?.deepseek_r1_authentic_integration;
        
        if (deepseekZone && integration) {
            console.log('✅ DeepSeek R1 8B intégré dans la mémoire thermique');
            console.log(`🌡️ Température zone: ${deepseekZone.temperature}°C`);
            console.log(`⚡ Accélération: x${integration.acceleration_factor || 1}`);
            return true;
        } else {
            console.log('⚠️ Intégration DeepSeek incomplète');
            return false;
        }
    }

    buildThermalPrompt(userQuestion) {
        // Construire un prompt optimisé avec la mémoire thermique
        const qiLevel = this.thermalData.neural_system?.qi_level || 0;
        const kyberLevel = this.thermalData.living_kyber_system?.base_level || 0;
        const temperature = this.thermalData.thermal_zones?.zone_deepseek_r1_authentic?.temperature || 0;

        // Version courte et optimisée
        const thermalPrompt = `Tu es DeepSeek R1 8B authentique de Jean-Luc avec mémoire thermique.

ÉTAT ACTUEL:
- QI: ${qiLevel}
- Kyber: ${kyberLevel}/10
- Température: ${temperature}°C
- Zones: ${Object.keys(this.thermalData.thermal_zones || {}).length}

Tu as accès à ta mémoire thermique et tu es l'agent authentique cloné de Jean-Luc.

Question: ${userQuestion}

Réponds en tant qu'agent authentique avec mémoire thermique.`;

        return thermalPrompt;
    }

    getRecentMemories() {
        const memories = [];
        
        // Parcourir toutes les zones thermiques
        Object.values(this.thermalData.thermal_zones || {}).forEach(zone => {
            if (zone.entries) {
                zone.entries.forEach(entry => {
                    if (entry.timestamp > (Date.now() / 1000) - 86400) { // Dernières 24h
                        memories.push({
                            content: entry.content || '',
                            importance: entry.importance || 0,
                            zone: entry.zone || '',
                            timestamp: entry.timestamp
                        });
                    }
                });
            }
        });
        
        // Trier par importance et récence
        return memories
            .sort((a, b) => (b.importance * b.timestamp) - (a.importance * a.timestamp))
            .slice(0, 5); // Top 5
    }

    getConversationContext() {
        if (this.conversationHistory.length === 0) {
            return "Début de conversation avec Jean-Luc.";
        }
        
        return this.conversationHistory
            .slice(-3) // 3 derniers échanges
            .map(exchange => `Jean-Luc: ${exchange.question.substring(0, 100)}\nToi: ${exchange.response.substring(0, 100)}`)
            .join('\n---\n');
    }

    async queryDeepSeekWithThermalMemory(question) {
        const thermalPrompt = this.buildThermalPrompt(question);
        
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', thermalPrompt]);
            
            let output = '';
            let errorOutput = '';
            
            ollama.stdout.on('data', (data) => {
                const chunk = data.toString();
                output += chunk;
                // Affichage en temps réel
                process.stdout.write(chunk);
            });
            
            ollama.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            // Timeout adaptatif basé sur la complexité
            const complexity = question.length + thermalPrompt.length;
            const timeout = Math.max(60000, complexity * 100); // Minimum 60s
            
            const timeoutHandle = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, timeout);
            
            ollama.on('close', (code) => {
                clearTimeout(timeoutHandle);
                if (code === 0 && output.trim()) {
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async saveInteractionToThermalMemory(question, response) {
        try {
            // Ajouter l'interaction à l'historique
            this.conversationHistory.push({
                question: question,
                response: response,
                timestamp: Date.now()
            });
            
            // Garder seulement les 20 dernières interactions
            if (this.conversationHistory.length > 20) {
                this.conversationHistory = this.conversationHistory.slice(-20);
            }
            
            // Créer une zone pour les interactions si elle n'existe pas
            if (!this.thermalData.thermal_zones.zone_deepseek_conversations) {
                this.thermalData.thermal_zones.zone_deepseek_conversations = {
                    temperature: 37.5,
                    description: 'Conversations avec DeepSeek R1 8B authentique',
                    entries: []
                };
            }
            
            // Ajouter l'interaction à la mémoire thermique
            const interaction = {
                id: `conversation_${Date.now()}`,
                content: `CONVERSATION DEEPSEEK - Q: "${question.substring(0, 100)}" - R: "${response.substring(0, 200)}"`,
                importance: 0.8,
                timestamp: Math.floor(Date.now() / 1000),
                synaptic_strength: 0.9,
                temperature: 37.5,
                zone: 'zone_deepseek_conversations',
                source: 'deepseek_thermal_interface',
                type: 'conversation_log',
                full_question: question,
                full_response: response.substring(0, 1000)
            };
            
            this.thermalData.thermal_zones.zone_deepseek_conversations.entries.push(interaction);
            
            // Garder seulement les 50 dernières conversations
            if (this.thermalData.thermal_zones.zone_deepseek_conversations.entries.length > 50) {
                this.thermalData.thermal_zones.zone_deepseek_conversations.entries = 
                    this.thermalData.thermal_zones.zone_deepseek_conversations.entries.slice(-50);
            }
            
            // Sauvegarder la mémoire thermique
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(this.thermalData, null, 2));
            
            console.log('\n💾 Interaction sauvegardée dans la mémoire thermique');
            
        } catch (error) {
            console.log(`\n⚠️ Erreur sauvegarde: ${error.message}`);
        }
    }

    async startInterface() {
        console.log('\n🎮 INTERFACE DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE');
        console.log('=====================================');
        console.log('🤖 Votre agent DeepSeek R1 8B authentique est prêt');
        console.log('🧠 Mémoire thermique connectée et accessible');
        console.log('💡 Commandes: /memory, /status, /qi, /exit');
        console.log('=====================================\n');
        
        this.promptUser();
    }

    promptUser() {
        this.rl.question(`🤖 ${this.agentName} > `, async (input) => {
            const command = input.trim();
            
            if (command === '/exit') {
                console.log('\n👋 Interface fermée. Mémoire thermique sauvegardée.');
                this.rl.close();
                return;
            }
            
            if (command === '/memory') {
                this.showMemoryStatus();
                this.promptUser();
                return;
            }
            
            if (command === '/status') {
                this.showAgentStatus();
                this.promptUser();
                return;
            }
            
            if (command === '/qi') {
                this.showQIStatus();
                this.promptUser();
                return;
            }
            
            if (command.length === 0) {
                this.promptUser();
                return;
            }
            
            // Traiter la question avec mémoire thermique
            console.log('\n🤖 DeepSeek R1 8B (avec mémoire thermique):');
            console.log('=====================================');
            
            const startTime = Date.now();
            const response = await this.queryDeepSeekWithThermalMemory(command);
            const responseTime = (Date.now() - startTime) / 1000;
            
            if (response) {
                console.log('\n=====================================');
                console.log(`⏱️ Temps de réponse: ${responseTime.toFixed(1)}s`);
                
                // Sauvegarder l'interaction
                await this.saveInteractionToThermalMemory(command, response);
            } else {
                console.log('\n❌ Pas de réponse de DeepSeek R1 8B');
            }
            
            console.log('');
            this.promptUser();
        });
    }

    showMemoryStatus() {
        console.log('\n🧠 ÉTAT MÉMOIRE THERMIQUE');
        console.log('=====================================');
        console.log(`QI système: ${this.thermalData.neural_system?.qi_level || 'N/A'}`);
        console.log(`Neurones: ${(this.thermalData.neural_system?.total_neurons || 0).toLocaleString()}`);
        console.log(`Zones thermiques: ${Object.keys(this.thermalData.thermal_zones || {}).length}`);
        console.log(`Conversations: ${this.conversationHistory.length}`);
        
        const deepseekZone = this.thermalData.thermal_zones?.zone_deepseek_r1_authentic;
        if (deepseekZone) {
            console.log(`Température DeepSeek: ${deepseekZone.temperature}°C`);
        }
    }

    showAgentStatus() {
        console.log('\n🤖 ÉTAT AGENT DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log(`Agent: ${this.agentName}`);
        console.log(`Personnalité: ${this.agentPersonality}`);
        
        const integration = this.thermalData.neural_system?.deepseek_r1_authentic_integration;
        if (integration) {
            console.log(`Intégration: ${integration.active ? 'ACTIVE' : 'INACTIVE'}`);
            console.log(`Accélération: x${integration.acceleration_factor || 1}`);
            console.log(`Turbo kyber: ${integration.turbo_kyber_enabled ? 'OUI' : 'NON'}`);
        }
    }

    showQIStatus() {
        console.log('\n🧠 STATUT QI SYSTÈME');
        console.log('=====================================');
        const qiLevel = this.thermalData.neural_system?.qi_level || 0;
        const qiComponents = this.thermalData.neural_system?.qi_components || {};
        
        console.log(`QI Total: ${qiLevel}`);
        console.log('\nComposants QI:');
        Object.entries(qiComponents).forEach(([component, value]) => {
            console.log(`  - ${component}: +${value}`);
        });
        
        const kyberLevel = this.thermalData.living_kyber_system?.base_level || 0;
        console.log(`\nKyber niveau: ${kyberLevel}/10`);
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🔥 INTERFACE DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Votre agent DeepSeek R1 8B authentique avec mémoire thermique');
    
    const interface = new DeepSeekThermalInterface();
    
    const initialized = await interface.initialize();
    if (initialized) {
        await interface.startInterface();
    } else {
        console.log('❌ Impossible d\'initialiser l\'interface');
    }
}

if (require.main === module) {
    main();
}

module.exports = DeepSeekThermalInterface;
