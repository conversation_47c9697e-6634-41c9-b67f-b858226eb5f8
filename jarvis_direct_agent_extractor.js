#!/usr/bin/env node

/**
 * 🧬 EXTRACTEUR DIRECT AGENT JARVIS
 * Extraction directe depuis les fichiers de l'application
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class JarvisDirectExtractor {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.jarvisAppPaths = [
            '/Applications/Louna AI.app/Contents/Resources',
            '/Applications/JARVIS.app/Contents/Resources',
            './cloned_agents/deepseek_r1_authentic_1749984237489/ollama_models/LOUNA-AI-COMPLET'
        ];
        this.extractedAgent = null;
        
        console.log('🧬 EXTRACTEUR DIRECT AGENT JARVIS');
        console.log('🎯 Recherche de l\'agent dans les fichiers...');
    }

    // Trouver l'application JARVIS
    findJarvisApp() {
        console.log('🔍 Recherche application JARVIS...');
        
        for (const appPath of this.jarvisAppPaths) {
            if (fs.existsSync(appPath)) {
                console.log(`✅ Application trouvée: ${appPath}`);
                return appPath;
            }
        }
        
        // Recherche dans le répertoire courant
        const currentFiles = fs.readdirSync('.');
        const serverFiles = currentFiles.filter(file => 
            file.includes('server') && file.endsWith('.js')
        );
        
        if (serverFiles.length > 0) {
            console.log(`✅ Serveurs trouvés: ${serverFiles.join(', ')}`);
            return '.';
        }
        
        throw new Error('❌ Application JARVIS non trouvée');
    }

    // Extraire l'agent depuis les fichiers
    async extractAgentFromFiles() {
        console.log('🧬 EXTRACTION AGENT DEPUIS FICHIERS...');
        
        const appPath = this.findJarvisApp();
        
        const agentData = {
            extraction_source: 'JARVIS_DIRECT_FILES',
            extraction_timestamp: Date.now(),
            app_path: appPath,
            agent_type: 'JARVIS_REAL_FILES',
            components: {},
            interfaces: {},
            thermal_integration: true,
            real_extraction: true
        };
        
        // Analyser les fichiers serveur
        const serverFiles = this.findServerFiles(appPath);
        for (const serverFile of serverFiles) {
            try {
                const serverData = this.analyzeServerFile(serverFile);
                agentData.components[path.basename(serverFile)] = serverData;
                console.log(`✅ Serveur analysé: ${path.basename(serverFile)}`);
            } catch (error) {
                console.log(`⚠️ Erreur analyse ${serverFile}:`, error.message);
            }
        }
        
        // Analyser les interfaces HTML
        const htmlFiles = this.findHtmlFiles(appPath);
        for (const htmlFile of htmlFiles) {
            try {
                const htmlData = this.analyzeHtmlFile(htmlFile);
                agentData.interfaces[path.basename(htmlFile)] = htmlData;
                console.log(`✅ Interface analysée: ${path.basename(htmlFile)}`);
            } catch (error) {
                console.log(`⚠️ Erreur analyse ${htmlFile}:`, error.message);
            }
        }
        
        this.extractedAgent = agentData;
        return agentData;
    }

    // Trouver les fichiers serveur
    findServerFiles(appPath) {
        const files = [];
        
        try {
            const dirFiles = fs.readdirSync(appPath);
            for (const file of dirFiles) {
                const filePath = path.join(appPath, file);
                if (file.endsWith('.js') && (
                    file.includes('server') || 
                    file.includes('main') ||
                    file.includes('deepseek')
                )) {
                    files.push(filePath);
                }
            }
        } catch (error) {
            console.log(`⚠️ Erreur lecture répertoire ${appPath}`);
        }
        
        return files;
    }

    // Trouver les fichiers HTML
    findHtmlFiles(appPath) {
        const files = [];
        
        try {
            const dirFiles = fs.readdirSync(appPath);
            for (const file of dirFiles) {
                const filePath = path.join(appPath, file);
                if (file.endsWith('.html') && (
                    file.includes('interface') || 
                    file.includes('louna') ||
                    file.includes('jarvis')
                )) {
                    files.push(filePath);
                }
            }
        } catch (error) {
            console.log(`⚠️ Erreur lecture répertoire ${appPath}`);
        }
        
        return files;
    }

    // Analyser un fichier serveur
    analyzeServerFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        return {
            path: filePath,
            size: content.length,
            has_express: content.includes('express'),
            has_socket: content.includes('socket'),
            has_thermal: content.includes('thermique') || content.includes('thermal'),
            has_deepseek: content.includes('deepseek') || content.includes('DeepSeek'),
            has_ollama: content.includes('ollama'),
            has_brain: content.includes('brain') || content.includes('cerveau'),
            ports: this.extractPorts(content),
            api_endpoints: this.extractApiEndpoints(content),
            analysis_timestamp: Date.now()
        };
    }

    // Analyser un fichier HTML
    analyzeHtmlFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        return {
            path: filePath,
            size: content.length,
            title: this.extractTitle(content),
            has_jarvis: content.includes('JARVIS') || content.includes('jarvis'),
            has_louna: content.includes('LOUNA') || content.includes('louna'),
            has_deepseek: content.includes('DeepSeek') || content.includes('deepseek'),
            has_thermal: content.includes('thermique') || content.includes('thermal'),
            has_brain: content.includes('cerveau') || content.includes('brain'),
            has_websocket: content.includes('WebSocket') || content.includes('socket.io'),
            analysis_timestamp: Date.now()
        };
    }

    // Extraire les ports
    extractPorts(content) {
        const ports = [];
        const portMatches = content.matchAll(/(?:PORT|port).*?(\d{4,5})/g);
        for (const match of portMatches) {
            ports.push(parseInt(match[1]));
        }
        return [...new Set(ports)]; // Supprimer les doublons
    }

    // Extraire les endpoints API
    extractApiEndpoints(content) {
        const endpoints = [];
        const matches = content.matchAll(/app\.(get|post|put|delete)\(['"`]([^'"`]+)['"`]/g);
        for (const match of matches) {
            endpoints.push({
                method: match[1].toUpperCase(),
                path: match[2]
            });
        }
        return endpoints;
    }

    // Extraire le titre HTML
    extractTitle(content) {
        const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i);
        return titleMatch ? titleMatch[1] : 'Sans titre';
    }

    // Intégrer dans la mémoire thermique
    async integrateIntoThermalMemory() {
        console.log('🌡️ INTÉGRATION DANS MÉMOIRE THERMIQUE...');
        
        if (!this.extractedAgent) {
            await this.extractAgentFromFiles();
        }
        
        // Lire la mémoire thermique
        let thermalMemory = {};
        if (fs.existsSync(this.thermalMemoryPath)) {
            thermalMemory = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            console.log('✅ Mémoire thermique chargée');
        }
        
        // Créer l'entrée agent JARVIS
        const jarvisAgentEntry = {
            agent_id: `jarvis_direct_${Date.now()}`,
            extraction_source: this.extractedAgent.extraction_source,
            extraction_timestamp: this.extractedAgent.extraction_timestamp,
            agent_type: 'JARVIS_REAL_FILES_INTEGRATED',
            app_path: this.extractedAgent.app_path,
            components: this.extractedAgent.components,
            interfaces: this.extractedAgent.interfaces,
            thermal_integration: {
                integrated: true,
                integration_timestamp: Date.now(),
                qi_boost: 75,
                neural_allocation: 2000000000,
                memory_zones: ['zone1_working', 'zone2_episodic', 'zone3_procedural'],
                status: 'ACTIVE_THERMAL_DIRECT'
            }
        };
        
        // Intégrer dans la mémoire thermique
        if (!thermalMemory.jarvis_agents) {
            thermalMemory.jarvis_agents = {};
        }
        
        thermalMemory.jarvis_agents[jarvisAgentEntry.agent_id] = jarvisAgentEntry;
        
        // Mettre à jour le QI
        if (thermalMemory.neural_system && thermalMemory.neural_system.qi_components) {
            thermalMemory.neural_system.qi_components.jarvis_direct_integration = 75;
            
            // Recalculer le QI total
            const totalQI = Object.values(thermalMemory.neural_system.qi_components)
                .reduce((sum, value) => sum + value, 0);
            thermalMemory.neural_system.qi_level = totalQI;
        }
        
        // Sauvegarder
        const backupPath = `${this.thermalMemoryPath}.backup_jarvis_direct_${Date.now()}`;
        fs.copyFileSync(this.thermalMemoryPath, backupPath);
        fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(thermalMemory, null, 2));
        
        console.log(`✅ Agent JARVIS intégré dans mémoire thermique`);
        console.log(`🆔 Agent ID: ${jarvisAgentEntry.agent_id}`);
        console.log(`🧠 QI boost: +${jarvisAgentEntry.thermal_integration.qi_boost}`);
        console.log(`💾 Backup: ${backupPath}`);
        
        return {
            agent_id: jarvisAgentEntry.agent_id,
            integration_success: true,
            qi_boost: jarvisAgentEntry.thermal_integration.qi_boost,
            backup_path: backupPath,
            components_count: Object.keys(this.extractedAgent.components).length,
            interfaces_count: Object.keys(this.extractedAgent.interfaces).length
        };
    }

    // Générer rapport
    generateReport(integrationResult) {
        console.log('\n📊 RAPPORT EXTRACTION JARVIS DIRECT');
        console.log('====================================');
        console.log(`🧬 Source: ${this.extractedAgent.extraction_source}`);
        console.log(`📁 Chemin: ${this.extractedAgent.app_path}`);
        console.log(`🔧 Composants: ${integrationResult.components_count}`);
        console.log(`🌐 Interfaces: ${integrationResult.interfaces_count}`);
        console.log(`🆔 Agent ID: ${integrationResult.agent_id}`);
        console.log(`🧠 QI boost: +${integrationResult.qi_boost}`);
        console.log(`✅ Intégration: ${integrationResult.integration_success ? 'RÉUSSIE' : 'ÉCHEC'}`);
        console.log(`⏱️ Terminé: ${new Date().toISOString()}`);
    }
}

async function main() {
    const extractor = new JarvisDirectExtractor();
    
    try {
        const integrationResult = await extractor.integrateIntoThermalMemory();
        extractor.generateReport(integrationResult);
    } catch (error) {
        console.error('❌ Erreur extraction:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = JarvisDirectExtractor;
