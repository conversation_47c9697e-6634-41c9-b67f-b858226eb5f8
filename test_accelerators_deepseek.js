#!/usr/bin/env node

/**
 * 🚀 TEST ACCÉLÉRATEURS DEEPSEEK R1 8B
 * 
 * Vérification que les accélérateurs fonctionnent avec l'agent cloné
 * Test des performances et de l'auto-gestion
 * 
 * Jean-Luc PASSAVE - 2025
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');

class AcceleratorTester {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.testResults = [];
    }

    async testAccelerators() {
        console.log('🚀 TEST ACCÉLÉRATEURS DEEPSEEK R1 8B');
        console.log('=====================================');
        console.log('🎯 Vérification auto-gestion et accélération');
        
        try {
            // 1. Vérifier l'état du système
            console.log('\n🔍 Étape 1: Analyse du système...');
            const systemInfo = await this.analyzeSystem();
            
            // 2. Test de performance avec charge
            console.log('\n⚡ Étape 2: Test de performance...');
            const performanceTest = await this.testPerformance();
            
            // 3. Vérifier l'auto-gestion
            console.log('\n🤖 Étape 3: Test auto-gestion...');
            const autoManagementTest = await this.testAutoManagement();
            
            // 4. Test accélération GPU/Neural
            console.log('\n🧠 Étape 4: Test accélération neural...');
            const accelerationTest = await this.testNeuralAcceleration();
            
            // 5. Rapport final
            const report = this.generateAcceleratorReport(systemInfo, performanceTest, autoManagementTest, accelerationTest);
            
            console.log('\n📊 RÉSULTATS ACCÉLÉRATEURS');
            console.log('=====================================');
            console.log(`🖥️ GPU détecté: ${systemInfo.hasGPU ? 'OUI' : 'NON'}`);
            console.log(`⚡ Accélération active: ${accelerationTest.accelerated ? 'OUI' : 'NON'}`);
            console.log(`🤖 Auto-gestion: ${autoManagementTest.selfManaged ? 'OUI' : 'NON'}`);
            console.log(`📈 Performance: ${performanceTest.performance_score}/10`);
            console.log(`🧠 Clone authentique: ${report.is_authentic_clone ? 'CONFIRMÉ' : 'DOUTEUX'}`);
            
            return report;
            
        } catch (error) {
            console.error('\n❌ ERREUR LORS DU TEST');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async analyzeSystem() {
        console.log('🔍 Analyse du système et des accélérateurs...');
        
        const systemInfo = {
            hasGPU: false,
            gpuInfo: '',
            ollamaVersion: '',
            deepseekModel: false,
            memoryUsage: 0,
            cpuInfo: ''
        };
        
        try {
            // Vérifier GPU (Metal sur Mac)
            const gpuCheck = await this.executeCommand('system_profiler SPDisplaysDataType');
            systemInfo.hasGPU = gpuCheck.includes('Metal') || gpuCheck.includes('GPU');
            systemInfo.gpuInfo = gpuCheck.split('\n').find(line => line.includes('Chipset')) || 'N/A';
            
            console.log(`✅ GPU: ${systemInfo.hasGPU ? 'DÉTECTÉ' : 'NON DÉTECTÉ'}`);
            if (systemInfo.gpuInfo !== 'N/A') {
                console.log(`📊 ${systemInfo.gpuInfo.trim()}`);
            }
            
            // Vérifier Ollama
            const ollamaVersion = await this.executeCommand('ollama --version');
            systemInfo.ollamaVersion = ollamaVersion.trim();
            console.log(`✅ Ollama: ${systemInfo.ollamaVersion}`);
            
            // Vérifier DeepSeek
            const ollamaList = await this.executeCommand('ollama list');
            systemInfo.deepseekModel = ollamaList.includes('deepseek-r1:8b');
            console.log(`✅ DeepSeek R1 8B: ${systemInfo.deepseekModel ? 'DISPONIBLE' : 'MANQUANT'}`);
            
        } catch (error) {
            console.log(`⚠️ Erreur analyse système: ${error.message}`);
        }
        
        return systemInfo;
    }

    async testPerformance() {
        console.log('⚡ Test de performance avec charge...');
        
        const performanceTest = {
            response_time: 0,
            thinking_time: 0,
            tokens_per_second: 0,
            performance_score: 0,
            accelerated: false
        };
        
        try {
            // Test avec une question complexe qui nécessite de la réflexion
            const complexQuestion = "Résous cette équation complexe: (x^2 + 5x - 6) / (x - 1) = 0, puis explique chaque étape de ton raisonnement.";
            
            console.log('🧮 Question complexe pour tester les accélérateurs...');
            const startTime = Date.now();
            
            const response = await this.queryDeepSeekWithTiming(complexQuestion);
            
            const totalTime = (Date.now() - startTime) / 1000;
            
            if (response) {
                // Analyser la réponse pour extraire les métriques
                const hasThinking = response.includes('Thinking...');
                const thinkingMatch = response.match(/Thinking\.\.\.(.*?)\.\.\.done thinking\./s);
                
                if (thinkingMatch) {
                    const thinkingText = thinkingMatch[1];
                    performanceTest.thinking_time = thinkingText.length / 100; // Estimation
                }
                
                performanceTest.response_time = totalTime;
                performanceTest.tokens_per_second = response.length / totalTime;
                
                // Score basé sur la vitesse et la qualité
                if (totalTime < 30 && hasThinking) {
                    performanceTest.performance_score = 9;
                    performanceTest.accelerated = true;
                } else if (totalTime < 60 && hasThinking) {
                    performanceTest.performance_score = 7;
                    performanceTest.accelerated = true;
                } else if (hasThinking) {
                    performanceTest.performance_score = 5;
                } else {
                    performanceTest.performance_score = 2;
                }
                
                console.log(`✅ Temps de réponse: ${totalTime.toFixed(1)}s`);
                console.log(`✅ Processus "Thinking": ${hasThinking ? 'DÉTECTÉ' : 'ABSENT'}`);
                console.log(`✅ Vitesse: ${performanceTest.tokens_per_second.toFixed(1)} tokens/s`);
                console.log(`✅ Score performance: ${performanceTest.performance_score}/10`);
                
            } else {
                console.log('❌ Pas de réponse obtenue');
                performanceTest.performance_score = 0;
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur test performance: ${error.message}`);
        }
        
        return performanceTest;
    }

    async testAutoManagement() {
        console.log('🤖 Test de l\'auto-gestion du système...');
        
        const autoTest = {
            selfManaged: false,
            memoryManagement: false,
            resourceOptimization: false,
            autonomousOperation: false
        };
        
        try {
            // Vérifier la mémoire thermique
            if (fs.existsSync(this.thermalMemoryPath)) {
                const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
                
                // Vérifier l'auto-gestion de la mémoire
                const hasDeepSeekZone = thermalData.thermal_zones?.zone_deepseek_r1_authentic;
                const qiLevel = thermalData.neural_system?.qi_level || 0;
                const neuronCount = thermalData.neural_system?.total_neurons || 0;
                
                autoTest.memoryManagement = hasDeepSeekZone && qiLevel > 700;
                
                console.log(`✅ Gestion mémoire: ${autoTest.memoryManagement ? 'AUTO-GÉRÉE' : 'MANUELLE'}`);
                console.log(`✅ QI système: ${qiLevel} (auto-ajusté)`);
                console.log(`✅ Neurones: ${neuronCount.toLocaleString()} (auto-alloués)`);
            }
            
            // Test d'opération autonome
            const autonomousResponse = await this.queryDeepSeekSimple("Bonjour");
            autoTest.autonomousOperation = autonomousResponse && autonomousResponse.length > 0;
            
            console.log(`✅ Opération autonome: ${autoTest.autonomousOperation ? 'FONCTIONNELLE' : 'PROBLÈME'}`);
            
            // Vérifier l'optimisation des ressources
            autoTest.resourceOptimization = autoTest.memoryManagement && autoTest.autonomousOperation;
            
            autoTest.selfManaged = autoTest.memoryManagement && autoTest.autonomousOperation && autoTest.resourceOptimization;
            
            console.log(`✅ Auto-gestion globale: ${autoTest.selfManaged ? 'CONFIRMÉE' : 'PARTIELLE'}`);
            
        } catch (error) {
            console.log(`⚠️ Erreur test auto-gestion: ${error.message}`);
        }
        
        return autoTest;
    }

    async testNeuralAcceleration() {
        console.log('🧠 Test de l\'accélération neurale...');
        
        const accelTest = {
            accelerated: false,
            neural_processing: false,
            gpu_utilization: false,
            optimization_active: false,
            acceleration_factor: 1.0
        };
        
        try {
            // Test avec une tâche qui bénéficie de l'accélération
            const neuralTask = "Analyse ce pattern: 1, 1, 2, 3, 5, 8, 13, 21... Quel est le prochain nombre et pourquoi ?";
            
            console.log('🔢 Test pattern recognition (accélération neurale)...');
            const startTime = Date.now();
            
            const response = await this.queryDeepSeekWithTiming(neuralTask);
            const processingTime = (Date.now() - startTime) / 1000;
            
            if (response) {
                // Analyser la qualité de la réponse
                const correctAnswer = response.includes('34') || response.includes('Fibonacci');
                const hasReasoning = response.includes('Thinking...') || response.toLowerCase().includes('fibonacci');
                
                accelTest.neural_processing = correctAnswer && hasReasoning;
                
                // Estimer l'accélération basée sur la vitesse et la précision
                if (processingTime < 20 && correctAnswer) {
                    accelTest.accelerated = true;
                    accelTest.acceleration_factor = 3.0;
                } else if (processingTime < 40 && correctAnswer) {
                    accelTest.accelerated = true;
                    accelTest.acceleration_factor = 2.0;
                } else if (correctAnswer) {
                    accelTest.acceleration_factor = 1.5;
                }
                
                accelTest.optimization_active = accelTest.neural_processing && processingTime < 30;
                
                console.log(`✅ Temps traitement: ${processingTime.toFixed(1)}s`);
                console.log(`✅ Réponse correcte: ${correctAnswer ? 'OUI' : 'NON'}`);
                console.log(`✅ Raisonnement: ${hasReasoning ? 'DÉTECTÉ' : 'ABSENT'}`);
                console.log(`✅ Facteur accélération: x${accelTest.acceleration_factor}`);
                console.log(`✅ Accélération active: ${accelTest.accelerated ? 'OUI' : 'NON'}`);
                
            } else {
                console.log('❌ Pas de réponse pour le test neural');
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur test accélération: ${error.message}`);
        }
        
        return accelTest;
    }

    async queryDeepSeekWithTiming(question) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', question]);
            
            let output = '';
            let errorOutput = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            ollama.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            // Timeout après 90 secondes
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, 90000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                if (code === 0 && output.trim()) {
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async queryDeepSeekSimple(question) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', question]);
            
            let output = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            // Timeout court pour test simple
            const timeout = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(output.trim());
            }, 30000);
            
            ollama.on('close', (code) => {
                clearTimeout(timeout);
                resolve(output.trim());
            });
        });
    }

    async executeCommand(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    resolve(''); // Ne pas rejeter, juste retourner vide
                } else {
                    resolve(stdout);
                }
            });
        });
    }

    generateAcceleratorReport(systemInfo, performanceTest, autoManagementTest, accelerationTest) {
        const report = {
            timestamp: Date.now(),
            system_info: systemInfo,
            performance: performanceTest,
            auto_management: autoManagementTest,
            acceleration: accelerationTest,
            overall_score: 0,
            is_authentic_clone: false,
            recommendations: []
        };
        
        // Calculer le score global
        let score = 0;
        if (systemInfo.deepseekModel) score += 2;
        if (performanceTest.performance_score >= 7) score += 3;
        if (autoManagementTest.selfManaged) score += 3;
        if (accelerationTest.accelerated) score += 2;
        
        report.overall_score = score;
        report.is_authentic_clone = score >= 7; // Sur 10
        
        // Recommandations
        if (!systemInfo.hasGPU) {
            report.recommendations.push('GPU non détecté - performances limitées');
        }
        if (performanceTest.performance_score < 5) {
            report.recommendations.push('Performance faible - vérifier la configuration');
        }
        if (!autoManagementTest.selfManaged) {
            report.recommendations.push('Auto-gestion incomplète');
        }
        
        // Sauvegarder le rapport
        const reportPath = `accelerator_test_report_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📋 Rapport détaillé: ${reportPath}`);
        
        return report;
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🚀 TEST ACCÉLÉRATEURS DEEPSEEK R1 8B');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Vérification accélérateurs et auto-gestion');
    
    const tester = new AcceleratorTester();
    await tester.testAccelerators();
}

if (require.main === module) {
    main();
}

module.exports = AcceleratorTester;
