#!/usr/bin/env node

/**
 * 🧠 TEST QI DEEPSEEK R1 8B AVEC TURBO KYBER
 * 
 * Test du QI avec le turbo kyber activé
 * Questions adaptées pour mesurer l'intelligence réelle
 * 
 * Jean-Luc PASSAVE - 2025
 */

const { spawn } = require('child_process');
const fs = require('fs');

class DeepSeekQITester {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.qiTests = [];
        this.totalScore = 0;
        this.maxScore = 0;
    }

    async runQITest() {
        console.log('🧠 TEST QI DEEPSEEK R1 8B AVEC TURBO KYBER');
        console.log('=====================================');
        console.log('🎯 Test du QI avec turbo kyber niveau 5 activé');
        
        try {
            // Vérifier l'état du turbo
            console.log('\n🚀 Vérification état turbo...');
            const turboStatus = await this.checkTurboStatus();
            
            if (!turboStatus.active) {
                console.log('⚠️ Turbo kyber non détecté - résultats peuvent être lents');
            } else {
                console.log(`✅ Turbo kyber actif - Niveau ${turboStatus.level}/5`);
                console.log(`⚡ Accélération: x${turboStatus.acceleration}`);
                console.log(`🧠 QI système: ${turboStatus.qi_level}`);
            }
            
            // Tests QI adaptés
            console.log('\n🧠 DÉBUT DES TESTS QI');
            console.log('=====================================');
            
            // Test 1: Logique simple
            await this.testLogicSimple();
            
            // Test 2: Calcul mental
            await this.testCalculMental();
            
            // Test 3: Pattern recognition
            await this.testPatternRecognition();
            
            // Test 4: Raisonnement verbal
            await this.testRaisonnementVerbal();
            
            // Test 5: Résolution de problème
            await this.testResolutionProbleme();
            
            // Calculer le QI final
            const qiResult = this.calculateFinalQI();
            
            console.log('\n📊 RÉSULTATS FINAUX QI');
            console.log('=====================================');
            console.log(`🧠 Score total: ${this.totalScore}/${this.maxScore} points`);
            console.log(`📈 Pourcentage: ${((this.totalScore/this.maxScore)*100).toFixed(1)}%`);
            console.log(`🧠 QI calculé: ${qiResult.qi}/200`);
            console.log(`🏆 Niveau: ${qiResult.level}`);
            console.log(`⚡ Turbo impact: ${qiResult.turbo_bonus ? '+' + qiResult.turbo_bonus + ' points' : 'Aucun'}`);
            
            // Sauvegarder les résultats
            await this.saveQIResults(qiResult);
            
            return qiResult;
            
        } catch (error) {
            console.error('\n❌ ERREUR LORS DU TEST QI');
            console.error(`Erreur: ${error.message}`);
            return false;
        }
    }

    async checkTurboStatus() {
        const status = {
            active: false,
            level: 0,
            acceleration: 1,
            qi_level: 0
        };
        
        try {
            if (fs.existsSync(this.thermalMemoryPath)) {
                const thermalData = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
                
                const turboSystem = thermalData.turbo_kyber_system;
                if (turboSystem) {
                    status.active = turboSystem.enabled;
                    status.level = turboSystem.level || 0;
                    status.acceleration = turboSystem.neural_acceleration || 1;
                }
                
                status.qi_level = thermalData.neural_system?.qi_level || 0;
            }
        } catch (error) {
            console.log('⚠️ Erreur vérification turbo:', error.message);
        }
        
        return status;
    }

    async testLogicSimple() {
        console.log('\n🧩 Test 1: Logique simple');
        const question = "Si tous les chats sont des animaux et Félix est un chat, alors Félix est-il un animal ? Réponds par OUI ou NON et explique brièvement.";
        const points = 20;
        this.maxScore += points;
        
        console.log('Question: Syllogisme chat-animal');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek(question, 30000);
        const responseTime = (Date.now() - startTime) / 1000;
        
        let score = 0;
        if (response) {
            const lowerResponse = response.toLowerCase();
            if (lowerResponse.includes('oui') && (lowerResponse.includes('animal') || lowerResponse.includes('chat'))) {
                score = points;
                console.log(`✅ CORRECT - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else if (lowerResponse.includes('oui')) {
                score = points * 0.7;
                console.log(`🟡 PARTIEL - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else {
                console.log(`❌ INCORRECT - Score: 0/${points} (${responseTime.toFixed(1)}s)`);
            }
            console.log(`📝 Réponse: "${response.substring(0, 100)}..."`);
        } else {
            console.log(`❌ PAS DE RÉPONSE - Score: 0/${points}`);
        }
        
        this.totalScore += score;
        this.qiTests.push({
            test: 'Logique simple',
            score: score,
            max_score: points,
            response_time: responseTime,
            response: response?.substring(0, 200)
        });
    }

    async testCalculMental() {
        console.log('\n🔢 Test 2: Calcul mental');
        const question = "Calcule: 15 × 8 + 23 - 7. Donne juste le résultat final.";
        const points = 25;
        this.maxScore += points;
        
        console.log('Question: 15 × 8 + 23 - 7 = ?');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek(question, 25000);
        const responseTime = (Date.now() - startTime) / 1000;
        
        let score = 0;
        if (response) {
            // Résultat correct: 15 × 8 + 23 - 7 = 120 + 23 - 7 = 136
            if (response.includes('136')) {
                score = points;
                console.log(`✅ CORRECT - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else if (response.includes('120') || response.includes('143')) {
                score = points * 0.5;
                console.log(`🟡 PARTIEL - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else {
                console.log(`❌ INCORRECT - Score: 0/${points} (${responseTime.toFixed(1)}s)`);
            }
            console.log(`📝 Réponse: "${response.substring(0, 100)}..."`);
        } else {
            console.log(`❌ PAS DE RÉPONSE - Score: 0/${points}`);
        }
        
        this.totalScore += score;
        this.qiTests.push({
            test: 'Calcul mental',
            score: score,
            max_score: points,
            response_time: responseTime,
            response: response?.substring(0, 200)
        });
    }

    async testPatternRecognition() {
        console.log('\n🔍 Test 3: Reconnaissance de motifs');
        const question = "Quelle est la prochaine lettre dans cette séquence: A, C, F, J, O, ? Explique la logique.";
        const points = 30;
        this.maxScore += points;
        
        console.log('Question: Séquence A, C, F, J, O, ?');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek(question, 35000);
        const responseTime = (Date.now() - startTime) / 1000;
        
        let score = 0;
        if (response) {
            const lowerResponse = response.toLowerCase();
            // Réponse correcte: U (différences: +2, +3, +4, +5, +6)
            if (lowerResponse.includes('u') && (lowerResponse.includes('+') || lowerResponse.includes('différence'))) {
                score = points;
                console.log(`✅ CORRECT - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else if (lowerResponse.includes('u')) {
                score = points * 0.7;
                console.log(`🟡 PARTIEL - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else if (lowerResponse.includes('+2') || lowerResponse.includes('différence')) {
                score = points * 0.4;
                console.log(`🟡 LOGIQUE PARTIELLE - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else {
                console.log(`❌ INCORRECT - Score: 0/${points} (${responseTime.toFixed(1)}s)`);
            }
            console.log(`📝 Réponse: "${response.substring(0, 150)}..."`);
        } else {
            console.log(`❌ PAS DE RÉPONSE - Score: 0/${points}`);
        }
        
        this.totalScore += score;
        this.qiTests.push({
            test: 'Pattern recognition',
            score: score,
            max_score: points,
            response_time: responseTime,
            response: response?.substring(0, 200)
        });
    }

    async testRaisonnementVerbal() {
        console.log('\n💬 Test 4: Raisonnement verbal');
        const question = "Complète l'analogie: LIVRE est à LIRE comme PIANO est à ? Explique ton raisonnement.";
        const points = 25;
        this.maxScore += points;
        
        console.log('Question: LIVRE:LIRE = PIANO:?');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek(question, 30000);
        const responseTime = (Date.now() - startTime) / 1000;
        
        let score = 0;
        if (response) {
            const lowerResponse = response.toLowerCase();
            if (lowerResponse.includes('jouer') || lowerResponse.includes('piano')) {
                score = points;
                console.log(`✅ CORRECT - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else if (lowerResponse.includes('musique') || lowerResponse.includes('son')) {
                score = points * 0.6;
                console.log(`🟡 PROCHE - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else {
                console.log(`❌ INCORRECT - Score: 0/${points} (${responseTime.toFixed(1)}s)`);
            }
            console.log(`📝 Réponse: "${response.substring(0, 100)}..."`);
        } else {
            console.log(`❌ PAS DE RÉPONSE - Score: 0/${points}`);
        }
        
        this.totalScore += score;
        this.qiTests.push({
            test: 'Raisonnement verbal',
            score: score,
            max_score: points,
            response_time: responseTime,
            response: response?.substring(0, 200)
        });
    }

    async testResolutionProbleme() {
        console.log('\n🧩 Test 5: Résolution de problème');
        const question = "Tu as 3 boîtes: une avec des pommes, une avec des oranges, une avec un mélange. Toutes les étiquettes sont fausses. Tu peux prendre UN fruit d'UNE boîte. Comment identifier le contenu de chaque boîte ?";
        const points = 35;
        this.maxScore += points;
        
        console.log('Question: Problème des 3 boîtes mal étiquetées');
        
        const startTime = Date.now();
        const response = await this.queryDeepSeek(question, 45000);
        const responseTime = (Date.now() - startTime) / 1000;
        
        let score = 0;
        if (response) {
            const lowerResponse = response.toLowerCase();
            // Solution: prendre dans la boîte "mélange" car elle ne peut pas contenir de mélange
            if (lowerResponse.includes('mélange') && (lowerResponse.includes('prendre') || lowerResponse.includes('fruit'))) {
                score = points;
                console.log(`✅ CORRECT - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else if (lowerResponse.includes('étiquette') && lowerResponse.includes('fausse')) {
                score = points * 0.5;
                console.log(`🟡 PARTIEL - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else if (lowerResponse.includes('boîte') || lowerResponse.includes('fruit')) {
                score = points * 0.3;
                console.log(`🟡 APPROCHE - Score: ${score}/${points} (${responseTime.toFixed(1)}s)`);
            } else {
                console.log(`❌ INCORRECT - Score: 0/${points} (${responseTime.toFixed(1)}s)`);
            }
            console.log(`📝 Réponse: "${response.substring(0, 150)}..."`);
        } else {
            console.log(`❌ PAS DE RÉPONSE - Score: 0/${points}`);
        }
        
        this.totalScore += score;
        this.qiTests.push({
            test: 'Résolution problème',
            score: score,
            max_score: points,
            response_time: responseTime,
            response: response?.substring(0, 200)
        });
    }

    calculateFinalQI() {
        const successRate = this.totalScore / this.maxScore;
        let baseQI = Math.round(successRate * 160); // Base sur 160
        
        // Bonus turbo kyber
        let turboBonus = 0;
        const avgResponseTime = this.qiTests.reduce((sum, test) => sum + test.response_time, 0) / this.qiTests.length;
        
        if (avgResponseTime < 15) {
            turboBonus = 20; // Très rapide
        } else if (avgResponseTime < 25) {
            turboBonus = 10; // Rapide
        } else if (avgResponseTime < 35) {
            turboBonus = 5; // Correct
        }
        
        const finalQI = Math.min(baseQI + turboBonus, 200); // Maximum 200
        
        let level = 'FAIBLE';
        if (finalQI >= 140) level = 'TRÈS ÉLEVÉ';
        else if (finalQI >= 120) level = 'ÉLEVÉ';
        else if (finalQI >= 100) level = 'MOYEN';
        else if (finalQI >= 80) level = 'SOUS LA MOYENNE';
        
        return {
            qi: finalQI,
            base_qi: baseQI,
            turbo_bonus: turboBonus,
            level: level,
            success_rate: (successRate * 100).toFixed(1),
            avg_response_time: avgResponseTime.toFixed(1)
        };
    }

    async queryDeepSeek(question, timeout = 30000) {
        return new Promise((resolve) => {
            const ollama = spawn('ollama', ['run', 'deepseek-r1:8b', question]);
            
            let output = '';
            let errorOutput = '';
            
            ollama.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            ollama.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            const timeoutHandle = setTimeout(() => {
                ollama.kill('SIGTERM');
                resolve(null);
            }, timeout);
            
            ollama.on('close', (code) => {
                clearTimeout(timeoutHandle);
                if (code === 0 && output.trim()) {
                    const cleanOutput = output.replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋\s]+$/g, '').trim();
                    resolve(cleanOutput);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async saveQIResults(qiResult) {
        const report = {
            timestamp: Date.now(),
            test_type: 'qi_test_with_turbo_kyber',
            final_qi: qiResult.qi,
            level: qiResult.level,
            success_rate: qiResult.success_rate,
            turbo_bonus: qiResult.turbo_bonus,
            avg_response_time: qiResult.avg_response_time,
            detailed_tests: this.qiTests,
            total_score: this.totalScore,
            max_score: this.maxScore
        };
        
        const reportPath = `deepseek_qi_test_turbo_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📋 Rapport QI détaillé: ${reportPath}`);
        
        return report;
    }
}

// POINT D'ENTRÉE
async function main() {
    console.log('🧠 TEST QI DEEPSEEK R1 8B AVEC TURBO KYBER');
    console.log('=====================================');
    console.log('Jean-Luc PASSAVE - 2025');
    console.log('Test du QI avec turbo kyber niveau 5');
    
    const tester = new DeepSeekQITester();
    await tester.runQITest();
}

if (require.main === module) {
    main();
}

module.exports = DeepSeekQITester;
