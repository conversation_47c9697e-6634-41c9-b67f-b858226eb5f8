{"timestamp": 1749986982816, "operation": "old_agents_disconnection", "initial_state": {"total_zones": 16, "agent_zones": [{"name": "zone_jarvis_r1", "temperature": 37, "entries": 0, "description": "Zone JARVIS R1 8B extrait de l'interface", "is_agent_zone": true, "is_parasitic": true, "is_core": false}, {"name": "zone_real_agent", "temperature": 37, "entries": 7, "description": "Zone de l'agent réel cloné", "is_agent_zone": true, "is_parasitic": true, "is_core": false}, {"name": "zone_cloned_agents", "temperature": 37, "entries": 4, "description": "Zone des agents clonés (originaux intacts)", "is_agent_zone": true, "is_parasitic": true, "is_core": false}, {"name": "zone_cloned_real_agents", "temperature": 37, "entries": 3, "description": "Agents réels clonés", "is_agent_zone": true, "is_parasitic": true, "is_core": false}], "parasitic_zones": [{"name": "zone_jarvis_r1", "temperature": 37, "entries": 0, "description": "Zone JARVIS R1 8B extrait de l'interface", "is_agent_zone": true, "is_parasitic": true, "is_core": false}, {"name": "zone_real_agent", "temperature": 37, "entries": 7, "description": "Zone de l'agent réel cloné", "is_agent_zone": true, "is_parasitic": true, "is_core": false}, {"name": "zone_cloned_agents", "temperature": 37, "entries": 4, "description": "Zone des agents clonés (originaux intacts)", "is_agent_zone": true, "is_parasitic": true, "is_core": false}, {"name": "zone_cloned_real_agents", "temperature": 37, "entries": 3, "description": "Agents réels clonés", "is_agent_zone": true, "is_parasitic": true, "is_core": false}], "core_zones": [{"name": "zone1_working", "temperature": 1114.6797860760487, "entries": 4, "description": "", "is_agent_zone": false, "is_parasitic": false, "is_core": true}, {"name": "zone2_episodic", "temperature": 1114.4690224295198, "entries": 149, "description": "", "is_agent_zone": false, "is_parasitic": false, "is_core": true}, {"name": "zone3_procedural", "temperature": 1114.4694830257492, "entries": 35, "description": "", "is_agent_zone": false, "is_parasitic": false, "is_core": true}, {"name": "zone4_semantic", "temperature": 1114.4919256124333, "entries": 69, "description": "", "is_agent_zone": false, "is_parasitic": false, "is_core": true}, {"name": "zone5_emotional", "temperature": 1114.475194649451, "entries": 0, "description": "", "is_agent_zone": false, "is_parasitic": false, "is_core": true}, {"name": "zone6_meta", "temperature": 1114.4851434674592, "entries": 114, "description": "", "is_agent_zone": false, "is_parasitic": false, "is_core": true}, {"name": "zone_deepseek_r1_authentic", "temperature": 38.5, "entries": 1, "description": "Zone DeepSeek R1 8B Authentique Cloné (cp -r)", "is_agent_zone": false, "is_parasitic": false, "is_core": true}, {"name": "zone_security_system", "temperature": 35, "entries": 1, "description": "Zone système de sécurité - Accès restreint Jean-Luc", "is_agent_zone": false, "is_parasitic": false, "is_core": true}], "memory_usage": 391000, "connection_conflicts": []}, "disconnection": {"disconnected_count": 4, "preserved_count": 8, "memory_freed": 14000}, "cleanup": {"leaks_cleaned": 0, "orphaned_entries_removed": 4, "memory_optimized": true}, "optimization": {"optimized": true, "connections_strengthened": 0, "temperature_adjusted": true}, "disconnected_agents": ["zone_jarvis_r1", "zone_real_agent", "zone_cloned_agents", "zone_cloned_real_agents"], "preserved_zones": ["zone_deepseek_r1_authentic", "zone1_working", "zone2_episodic", "zone3_procedural", "zone4_semantic", "zone5_emotional", "zone6_meta", "zone_security_system"], "overall_success": true}